#!/usr/bin/env python3
"""
海事系统视频数据后端服务Web服务器启动脚本

启动FastAPI Web服务
"""

import sys
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.video_monitor.web.app import run_app


def setup_logging(debug: bool = False):
    """
    设置日志配置
    
    Args:
        debug: 是否开启调试模式
    """
    log_level = logging.DEBUG if debug else logging.INFO
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('video_monitor_web.log', encoding='utf-8')
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="海事系统视频数据后端服务Web服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_web_server.py                          # 使用默认配置启动
  python run_web_server.py --host 0.0.0.0 --port 8080  # 指定主机和端口
  python run_web_server.py --debug                  # 开启调试模式
  python run_web_server.py --help                   # 显示帮助信息

API文档地址:
  http://localhost:8080/docs                       # Swagger UI
  http://localhost:8080/redoc                      # ReDoc

主要API接口:
  GET  /api/v1/streams/uri/{channel_id}            # 获取视频流URI
  GET  /api/v1/organizations                       # 获取组织机构列表
  GET  /api/v1/channels                            # 获取视频通道列表
  POST /api/v1/sync/start                          # 启动数据同步
  GET  /api/v1/sync/status                         # 获取同步状态
        """
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='监听主机地址 (默认: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='监听端口 (默认: 8080)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='开启调试模式'
    )
    
    parser.add_argument(
        '--reload',
        action='store_true',
        help='开启自动重载 (开发模式)'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.debug)
    
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🚀 启动海事系统视频数据后端服务Web服务器")
        logger.info(f"📡 监听地址: {args.host}:{args.port}")
        logger.info(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
        logger.info(f"🔄 自动重载: {'开启' if args.reload else '关闭'}")
        logger.info(f"📚 API文档: http://{args.host}:{args.port}/docs")
        logger.info(f"🔗 健康检查: http://{args.host}:{args.port}/health")
        
        # 启动Web服务
        run_app(
            host=args.host,
            port=args.port,
            debug=args.debug or args.reload
        )
        
    except KeyboardInterrupt:
        logger.info("👋 收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("✅ 服务已关闭")


if __name__ == "__main__":
    main()
