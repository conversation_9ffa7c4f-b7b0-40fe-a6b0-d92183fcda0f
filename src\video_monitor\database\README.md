# 海事系统视频数据后端服务 - 数据库设置指南

## 概述

本目录包含海事系统视频数据后端服务的数据库设置脚本和相关文档。

## 文件说明

### SQL脚本文件

1. **setup_database.sql** - 主要数据库设置脚本
   - 分步骤创建所有必需的表
   - 包含触发器、策略和安全设置
   - 推荐使用此脚本进行初始化

2. **create_tables_step_by_step.sql** - 分步创建表脚本
   - 按步骤创建各个表
   - 适合逐步执行和调试

3. **create_indexes_safely.sql** - 安全创建索引脚本
   - 在创建索引前检查表和列是否存在
   - 避免因表不存在而导致的错误

4. **verify_tables.sql** - 表结构验证脚本
   - 检查所有表是否正确创建
   - 验证关键列是否存在
   - 显示表结构信息

5. **maritime_video_system.sql** - 完整系统脚本
   - 包含所有表、索引、触发器等
   - 一次性创建完整数据库结构

### Python模块文件

1. **supabase_client.py** - Supabase客户端封装
2. **models.py** - 数据模型定义

## 推荐执行顺序

### 方法一：使用主设置脚本（推荐）

```bash
# 1. 执行主设置脚本
psql -h 120.55.84.198 -p 8000 -d your_database -f setup_database.sql

# 2. 验证表结构
psql -h 120.55.84.198 -p 8000 -d your_database -f verify_tables.sql

# 3. 创建索引
psql -h 120.55.84.198 -p 8000 -d your_database -f create_indexes_safely.sql
```

### 方法二：分步执行

```bash
# 1. 分步创建表
psql -h 120.55.84.198 -p 8000 -d your_database -f create_tables_step_by_step.sql

# 2. 验证表结构
psql -h 120.55.84.198 -p 8000 -d your_database -f verify_tables.sql

# 3. 安全创建索引
psql -h 120.55.84.198 -p 8000 -d your_database -f create_indexes_safely.sql
```

## 故障排除

### 常见错误及解决方案

#### 1. "column does not exist" 错误

**错误信息：** `ERROR: 42703: column "channel_id" does not exist`

**原因：** 表创建失败或列名不正确

**解决方案：**
1. 首先运行 `verify_tables.sql` 检查表结构
2. 如果表不存在，重新运行 `setup_database.sql`
3. 使用 `create_indexes_safely.sql` 而不是直接创建索引

#### 2. 权限错误

**错误信息：** `permission denied` 或类似权限错误

**解决方案：**
1. 确保使用正确的数据库用户和密码
2. 检查用户是否有创建表和索引的权限
3. 联系数据库管理员获取必要权限

#### 3. 连接错误

**错误信息：** `connection refused` 或连接超时

**解决方案：**
1. 检查Supabase服务是否正常运行
2. 验证连接参数（主机、端口、数据库名）
3. 检查网络连接和防火墙设置

## 数据库表结构

### 核心表

1. **organizations** - 组织机构表
   - 存储海事系统的组织架构信息
   - 支持层级结构

2. **video_channels** - 视频通道表
   - 存储视频通道信息
   - 关联组织机构

3. **video_monitoring_points** - 视频监控点表
   - 详细的监控点信息
   - 包含设备和位置信息

### 日志表

1. **sync_logs** - 数据同步日志
   - 记录同步操作的历史
   - 包含成功/失败状态和统计信息

2. **api_access_logs** - API访问日志
   - 记录API调用信息
   - 用于监控和分析

### 会话表

1. **video_stream_sessions** - 视频流会话
   - 管理视频流访问会话
   - 支持会话过期和访问控制

2. **video_events** - 视频事件记录
   - 记录视频相关事件
   - 支持事件处理和通知

## 性能优化

### 索引策略

- 为常用查询字段创建索引
- 组合索引用于多字段查询
- 定期分析查询性能并优化索引

### 数据清理

建议定期清理以下数据：
- 过期的视频流会话
- 旧的API访问日志
- 已处理的视频事件

## 安全考虑

1. **行级安全性 (RLS)**
   - 所有表都启用了RLS
   - 当前策略允许所有操作（开发环境）
   - 生产环境需要根据实际需求调整策略

2. **数据加密**
   - 敏感信息（如设备密码）应加密存储
   - 使用适当的加密算法

3. **访问控制**
   - 限制数据库用户权限
   - 定期审查访问日志

## 维护建议

1. **定期备份**
   - 设置自动备份策略
   - 测试备份恢复流程

2. **监控**
   - 监控数据库性能
   - 设置告警机制

3. **更新**
   - 定期更新数据库版本
   - 应用安全补丁

## 联系信息

如有问题或需要支持，请联系开发团队。
