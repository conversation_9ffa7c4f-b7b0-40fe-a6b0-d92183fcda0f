# 海事系统视频数据后端服务 API 文档

## 概述

海事系统视频数据后端服务提供 RESTful API 接口，用于查询视频流 URI 地址、管理组织机构和视频通道信息、控制数据同步等功能。

## 基础信息

- **基础 URL**: `http://localhost:8080/api/v1`
- **API 版本**: v1.0.0
- **认证方式**: 无需认证（内部服务）
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {...}
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "timestamp": "2024-01-01T12:00:00Z",
  "error_code": "ERROR_CODE",
  "error_details": {...}
}
```

## API 接口

### 1. 视频流管理

#### 1.1 获取视频流 URI

**接口地址**: `GET /streams/uri/{channel_id}`

**描述**: 根据通道 ID 获取视频流 URI 地址

**路径参数**:

- `channel_id` (string, required): 视频通道 ID

**查询参数**:

- `protocol` (string, optional): 流协议类型，默认为"HLS"
  - 可选值: "HLS", "RTMP", "WebRTC"
- `quality` (string, optional): 视频质量，默认为"high"
  - 可选值: "high", "medium", "low"
- `session_duration` (integer, optional): 会话持续时间(秒)，默认为 3600

**请求示例**:

```bash
GET /api/v1/streams/uri/CH001?protocol=HLS&quality=high&session_duration=7200
```

**响应示例**:

```json
{
  "success": true,
  "message": "获取视频流URI成功",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "channel_id": "CH001",
    "channel_name": "港口监控点1",
    "stream_url": "http://example.com/stream/CH001.m3u8",
    "backup_stream_url": "http://backup.example.com/stream/CH001.m3u8",
    "protocol": "HLS",
    "session_token": "uuid-session-token",
    "expires_at": "2024-01-01T14:00:00Z"
  }
}
```

#### 1.2 刷新视频流会话

**接口地址**: `POST /streams/session/refresh`

**描述**: 延长现有视频流会话的有效期

**查询参数**:

- `session_token` (string, required): 会话令牌
- `extend_duration` (integer, optional): 延长时间(秒)，默认为 3600

**请求示例**:

```bash
POST /api/v1/streams/session/refresh?session_token=uuid-session-token&extend_duration=3600
```

#### 1.3 关闭视频流会话

**接口地址**: `DELETE /streams/session/{session_token}`

**描述**: 主动关闭指定的视频流会话

**路径参数**:

- `session_token` (string, required): 会话令牌

### 2. 组织机构管理

#### 2.1 获取组织机构列表

**接口地址**: `GET /organizations`

**描述**: 获取组织机构列表，支持分页和过滤

**查询参数**:

- `page` (integer, optional): 页码，默认为 1
- `page_size` (integer, optional): 每页数量，默认为 20，最大 100
- `org_id` (string, optional): 组织 ID 过滤
- `parent_id` (string, optional): 父组织 ID 过滤
- `level` (integer, optional): 组织级别过滤
- `search` (string, optional): 搜索关键词

**响应示例**:

```json
{
  "success": true,
  "message": "获取组织机构列表成功",
  "data": [
    {
      "org_id": "ORG001",
      "org_name": "海事局总部",
      "parent_id": null,
      "level": 0,
      "node_type": 1,
      "type_code": "01",
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1
}
```

#### 2.2 获取单个组织机构

**接口地址**: `GET /organizations/{org_id}`

**描述**: 根据组织 ID 获取单个组织机构信息

#### 2.3 获取组织机构子级

**接口地址**: `GET /organizations/{org_id}/children`

**描述**: 获取指定组织机构的子级

**查询参数**:

- `recursive` (boolean, optional): 是否递归获取所有子级，默认为 false

### 3. 视频通道管理

#### 3.1 获取视频通道列表

**接口地址**: `GET /channels`

**描述**: 获取视频通道列表，支持分页和过滤

**查询参数**:

- `page` (integer, optional): 页码，默认为 1
- `page_size` (integer, optional): 每页数量，默认为 20
- `org_id` (string, optional): 组织 ID 过滤
- `status` (string, optional): 状态过滤
- `device_type` (string, optional): 设备类型过滤
- `search` (string, optional): 搜索关键词

**响应示例**:

```json
{
  "success": true,
  "message": "获取视频通道列表成功",
  "data": [
    {
      "channel_id": "CH001",
      "channel_name": "港口监控点1",
      "org_id": "ORG001",
      "org_name": "海事局总部",
      "org_level": 0,
      "device_type": "网络摄像头",
      "status": "active",
      "stream_url": "http://example.com/stream/CH001",
      "backup_stream_url": null,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1
}
```

#### 3.2 获取单个视频通道

**接口地址**: `GET /channels/{channel_id}`

#### 3.3 根据组织获取视频通道

**接口地址**: `GET /channels/organization/{org_id}`

**查询参数**:

- `include_children` (boolean, optional): 是否包含子组织的通道
- `status` (string, optional): 状态过滤

#### 3.4 根据状态获取视频通道

**接口地址**: `GET /channels/status/{status}`

### 4. 数据同步管理

#### 4.1 启动数据同步

**接口地址**: `POST /sync/start`

**描述**: 手动触发数据同步操作

**请求体**:

```json
{
  "sync_type": "manual",
  "force": false
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "数据同步已启动",
  "data": {
    "sync_type": "manual",
    "status": "started",
    "message": "同步任务已在后台启动"
  }
}
```

#### 4.2 获取同步状态

**接口地址**: `GET /sync/status`

**描述**: 获取当前数据同步状态

**响应示例**:

```json
{
  "success": true,
  "message": "获取同步状态成功",
  "data": {
    "is_syncing": false,
    "statistics": {
      "total_organizations": 10,
      "total_channels": 50
    },
    "recent_sync_logs": [...]
  }
}
```

#### 4.3 同步组织机构

**接口地址**: `POST /sync/organizations`

#### 4.4 同步视频通道

**接口地址**: `POST /sync/channels`

#### 4.5 获取同步日志

**接口地址**: `GET /sync/logs`

**查询参数**:

- `page` (integer, optional): 页码
- `page_size` (integer, optional): 每页数量
- `sync_type` (string, optional): 同步类型过滤
- `sync_status` (string, optional): 同步状态过滤

### 5. 日志管理

#### 5.1 获取 API 访问日志

**接口地址**: `GET /logs/access`

**查询参数**:

- `page` (integer, optional): 页码
- `page_size` (integer, optional): 每页数量
- `endpoint` (string, optional): 接口地址过滤
- `method` (string, optional): 请求方法过滤
- `status_code` (integer, optional): 状态码过滤
- `start_date` (string, optional): 开始日期 (YYYY-MM-DD)
- `end_date` (string, optional): 结束日期 (YYYY-MM-DD)

#### 5.2 获取 API 访问统计

**接口地址**: `GET /logs/access/stats`

**查询参数**:

- `days` (integer, optional): 统计天数，默认为 7

#### 5.3 获取错误日志

**接口地址**: `GET /logs/errors`

## 状态码说明

- `200` - 请求成功
- `400` - 请求参数错误
- `404` - 资源不存在
- `409` - 资源冲突（如同步任务已在进行）
- `500` - 服务器内部错误
- `503` - 服务不可用

## 错误代码

- `HTTP_400` - 请求参数错误
- `HTTP_404` - 资源不存在
- `HTTP_409` - 资源冲突
- `HTTP_500` - 内部服务器错误
- `INTERNAL_SERVER_ERROR` - 内部服务器错误

## 使用示例

### Python 示例

```python
import requests

# 获取视频流URI
response = requests.get(
    "http://localhost:8080/api/v1/streams/uri/CH001",
    params={
        "protocol": "HLS",
        "quality": "high"
    }
)

if response.status_code == 200:
    data = response.json()
    if data["success"]:
        stream_url = data["data"]["stream_url"]
        print(f"视频流地址: {stream_url}")
```

### curl 示例

```bash
# 获取视频流URI
curl -X GET "http://localhost:8080/api/v1/streams/uri/CH001?protocol=HLS&quality=high"

# 启动数据同步
curl -X POST "http://localhost:8080/api/v1/sync/start" \
  -H "Content-Type: application/json" \
  -d '{"sync_type": "manual", "force": false}'

# 获取组织机构列表
curl -X GET "http://localhost:8080/api/v1/organizations?page=1&page_size=10"
```

## 快速开始

### 1. 启动服务

```bash
# 进入项目目录
cd src/video_monitor

# 启动Web服务器
python run_web_server.py --host 0.0.0.0 --port 8080

# 或者使用调试模式
python run_web_server.py --debug
```

### 2. 验证服务

```bash
# 健康检查
curl http://localhost:8080/health

# 查看API文档
# 浏览器访问: http://localhost:8080/docs
```

### 3. 基本使用流程

```bash
# 1. 首先启动数据同步
curl -X POST "http://localhost:8080/api/v1/sync/start" \
  -H "Content-Type: application/json" \
  -d '{"sync_type": "manual"}'

# 2. 查看同步状态
curl "http://localhost:8080/api/v1/sync/status"

# 3. 获取组织机构列表
curl "http://localhost:8080/api/v1/organizations"

# 4. 获取视频通道列表
curl "http://localhost:8080/api/v1/channels"

# 5. 获取视频流URI
curl "http://localhost:8080/api/v1/streams/uri/CH001?protocol=HLS"
```

## 注意事项

1. **会话管理**: 视频流会话有过期时间，需要定期刷新或重新获取
2. **并发限制**: 同一时间只能有一个数据同步任务运行
3. **日志清理**: 建议定期清理访问日志和同步日志以节省存储空间
4. **错误处理**: 客户端应该正确处理各种错误状态码和错误信息
5. **性能优化**: 大量数据查询时建议使用分页参数
6. **数据库连接**: 确保 Supabase 服务正常运行并且连接配置正确
7. **定时同步**: 系统会每小时自动执行一次数据同步
