"""
视频通道API路由

提供视频通道查询等功能
"""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, Query

from ..models import (
    VideoChannelModel, VideoChannelListResponse, ErrorResponse,
    PaginationParams, FilterParams
)
from ..app import get_app_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=VideoChannelListResponse)
async def get_video_channels(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    org_id: Optional[str] = Query(default=None, description="组织ID过滤"),
    status: Optional[str] = Query(default=None, description="状态过滤"),
    device_type: Optional[str] = Query(default=None, description="设备类型过滤"),
    search: Optional[str] = Query(default=None, description="搜索关键词")
):
    """
    获取视频通道列表
    
    支持分页、过滤和搜索功能
    
    Args:
        page: 页码
        page_size: 每页数量
        org_id: 组织ID过滤
        status: 状态过滤
        device_type: 设备类型过滤
        search: 搜索关键词
        
    Returns:
        视频通道列表响应
    """
    try:
        logger.info(f"获取视频通道列表: page={page}, page_size={page_size}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 构建过滤条件
        filters = {}
        if org_id:
            filters["org_id"] = org_id
        if status:
            filters["status"] = status
        if device_type:
            filters["device_type"] = device_type
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询数据
        channels = supabase_client.query_data(
            "video_channels",
            filters=filters,
            search_fields=["channel_name", "org_name"] if search else None,
            search_term=search,
            limit=page_size,
            offset=offset,
            order_by="org_level, org_name, channel_name"
        )
        
        # 查询总数
        total = supabase_client.count_data("video_channels", filters=filters)
        
        # 转换为模型
        channel_models = [VideoChannelModel(**channel) for channel in channels]
        
        return VideoChannelListResponse(
            success=True,
            message="获取视频通道列表成功",
            data=channel_models,
            total=total
        )
        
    except Exception as e:
        logger.error(f"获取视频通道列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取视频通道列表失败: {str(e)}"
        )


@router.get("/{channel_id}", response_model=VideoChannelModel)
async def get_video_channel(channel_id: str):
    """
    获取单个视频通道信息
    
    Args:
        channel_id: 通道ID
        
    Returns:
        视频通道信息
    """
    try:
        logger.info(f"获取视频通道信息: channel_id={channel_id}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 查询通道信息
        channels = supabase_client.query_data(
            "video_channels",
            filters={"channel_id": channel_id}
        )
        
        if not channels:
            raise HTTPException(
                status_code=404,
                detail=f"视频通道 {channel_id} 不存在"
            )
        
        return VideoChannelModel(**channels[0])
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取视频通道信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取视频通道信息失败: {str(e)}"
        )


@router.get("/organization/{org_id}", response_model=VideoChannelListResponse)
async def get_channels_by_organization(
    org_id: str,
    include_children: bool = Query(default=False, description="是否包含子组织的通道"),
    status: Optional[str] = Query(default=None, description="状态过滤")
):
    """
    根据组织ID获取视频通道
    
    Args:
        org_id: 组织ID
        include_children: 是否包含子组织的通道
        status: 状态过滤
        
    Returns:
        视频通道列表响应
    """
    try:
        logger.info(f"根据组织获取视频通道: org_id={org_id}, include_children={include_children}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 构建过滤条件
        filters = {}
        if status:
            filters["status"] = status
        
        if include_children:
            # 获取所有子组织ID
            from .organizations import _get_all_children_recursive
            children_orgs = _get_all_children_recursive(supabase_client, org_id)
            org_ids = [org_id] + [child["org_id"] for child in children_orgs]
            
            # 查询所有相关组织的通道
            all_channels = []
            for oid in org_ids:
                org_filters = {**filters, "org_id": oid}
                org_channels = supabase_client.query_data(
                    "video_channels",
                    filters=org_filters,
                    order_by="org_level, org_name, channel_name"
                )
                all_channels.extend(org_channels)
            
            channels = all_channels
        else:
            # 只查询指定组织的通道
            filters["org_id"] = org_id
            channels = supabase_client.query_data(
                "video_channels",
                filters=filters,
                order_by="channel_name"
            )
        
        # 转换为模型
        channel_models = [VideoChannelModel(**channel) for channel in channels]
        
        return VideoChannelListResponse(
            success=True,
            message="获取组织视频通道成功",
            data=channel_models,
            total=len(channel_models)
        )
        
    except Exception as e:
        logger.error(f"根据组织获取视频通道失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取组织视频通道失败: {str(e)}"
        )


@router.get("/status/{status}", response_model=VideoChannelListResponse)
async def get_channels_by_status(
    status: str,
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量")
):
    """
    根据状态获取视频通道
    
    Args:
        status: 通道状态
        page: 页码
        page_size: 每页数量
        
    Returns:
        视频通道列表响应
    """
    try:
        logger.info(f"根据状态获取视频通道: status={status}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询数据
        channels = supabase_client.query_data(
            "video_channels",
            filters={"status": status},
            limit=page_size,
            offset=offset,
            order_by="org_level, org_name, channel_name"
        )
        
        # 查询总数
        total = supabase_client.count_data("video_channels", filters={"status": status})
        
        # 转换为模型
        channel_models = [VideoChannelModel(**channel) for channel in channels]
        
        return VideoChannelListResponse(
            success=True,
            message=f"获取状态为 {status} 的视频通道成功",
            data=channel_models,
            total=total
        )
        
    except Exception as e:
        logger.error(f"根据状态获取视频通道失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取状态视频通道失败: {str(e)}"
        )
