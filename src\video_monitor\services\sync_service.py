"""
数据同步服务

负责将海事系统的组织机构和视频通道信息同步到Supabase数据库
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..database.supabase_client import SupabaseClient
from ..database.models import Organization, VideoChannel, SyncLog
from ..business.monitor_service import MonitorService
from ..models.data_models import ChannelInfo, OrganizationInfo

logger = logging.getLogger(__name__)


class SyncService:
    """数据同步服务类"""

    def __init__(
        self, supabase_client: SupabaseClient, monitor_service: MonitorService
    ):
        """
        初始化数据同步服务

        Args:
            supabase_client: Supabase客户端
            monitor_service: 监控服务
        """
        self.supabase_client = supabase_client
        self.monitor_service = monitor_service

        logger.info("数据同步服务初始化完成")

    def sync_all_data(self, sync_type: str = "manual") -> Dict[str, Any]:
        """
        同步所有数据（组织机构和视频通道）

        Args:
            sync_type: 同步类型（manual/scheduled）

        Returns:
            同步结果信息
        """
        logger.info(f"🔄 开始{sync_type}数据同步...")

        # 创建同步日志
        sync_log = SyncLog(sync_type=sync_type, sync_status="running")
        log_data = self.supabase_client.insert_data("sync_logs", sync_log.to_dict())
        if log_data:
            sync_log.id = (
                log_data[0].get("id")
                if isinstance(log_data, list)
                else log_data.get("id")
            )

        try:
            # 获取海事系统数据
            channels = self.monitor_service.get_sub()
            if not channels:
                error_msg = "从海事系统获取数据失败"
                logger.error(f"❌ {error_msg}")
                self._update_sync_log_error(sync_log.id, error_msg)
                return {"success": False, "error": error_msg}

            logger.info(f"📡 从海事系统获取到 {len(channels)} 个通道")

            # 提取组织机构信息
            organizations = self._extract_organizations(channels)
            logger.info(f"🏢 提取到 {len(organizations)} 个组织机构")

            # 同步组织机构
            org_sync_result = self._sync_organizations(organizations)

            # 同步视频通道
            channel_sync_result = self._sync_channels(channels)

            # 更新同步日志
            sync_log.sync_status = "success"
            sync_log.end_time = datetime.now()
            sync_log.organizations_count = len(organizations)
            sync_log.channels_count = len(channels)

            self.supabase_client.update_data(
                "sync_logs", sync_log.to_dict(), {"id": sync_log.id}
            )

            result = {
                "success": True,
                "sync_id": sync_log.id,
                "organizations": {
                    "total": len(organizations),
                    "inserted": org_sync_result.get("inserted", 0),
                    "updated": org_sync_result.get("updated", 0),
                },
                "channels": {
                    "total": len(channels),
                    "inserted": channel_sync_result.get("inserted", 0),
                    "updated": channel_sync_result.get("updated", 0),
                },
                "duration": str(sync_log.end_time - sync_log.start_time),
            }

            logger.info(f"✅ 数据同步完成: {result}")
            return result

        except Exception as e:
            error_msg = f"数据同步过程中发生异常: {e}"
            logger.error(f"❌ {error_msg}")
            self._update_sync_log_error(sync_log.id, error_msg)
            return {"success": False, "error": error_msg}

    def _extract_organizations(self, channels: List[ChannelInfo]) -> List[Organization]:
        """
        从通道信息中提取组织机构信息

        Args:
            channels: 通道信息列表

        Returns:
            组织机构列表
        """
        org_dict = {}

        for channel in channels:
            org_id = channel.org_id
            if org_id not in org_dict:
                org_dict[org_id] = Organization(
                    org_id=org_id,
                    org_name=channel.org_name,
                    level=channel.org_level,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )

        return list(org_dict.values())

    def _sync_organizations(self, organizations: List[Organization]) -> Dict[str, int]:
        """
        同步组织机构到数据库

        Args:
            organizations: 组织机构列表

        Returns:
            同步结果统计
        """
        logger.info(f"🏢 开始同步 {len(organizations)} 个组织机构...")

        inserted = 0
        updated = 0

        for org in organizations:
            try:
                # 检查组织是否已存在
                existing = self.supabase_client.select_data(
                    "organizations", filters={"org_id": org.org_id}
                )

                if existing:
                    # 更新现有组织
                    org.updated_at = datetime.now()
                    self.supabase_client.update_data(
                        "organizations", org.to_dict(), {"org_id": org.org_id}
                    )
                    updated += 1
                    logger.debug(f"✅ 更新组织: {org.org_name}")
                else:
                    # 插入新组织
                    self.supabase_client.insert_data("organizations", org.to_dict())
                    inserted += 1
                    logger.debug(f"✅ 插入组织: {org.org_name}")

            except Exception as e:
                logger.error(f"❌ 同步组织 {org.org_name} 失败: {e}")

        logger.info(f"✅ 组织机构同步完成，插入: {inserted}, 更新: {updated}")
        return {"inserted": inserted, "updated": updated}

    def _sync_channels(self, channels: List[ChannelInfo]) -> Dict[str, int]:
        """
        同步视频通道到数据库

        Args:
            channels: 通道信息列表

        Returns:
            同步结果统计
        """
        logger.info(f"📺 开始同步 {len(channels)} 个视频通道...")

        inserted = 0
        updated = 0

        for channel_info in channels:
            try:
                # 转换为数据库模型
                channel = VideoChannel(
                    channel_id=channel_info.channel_id,
                    channel_name=channel_info.channel_name,
                    org_id=channel_info.org_id,
                    org_name=channel_info.org_name,
                    org_level=channel_info.org_level,
                    status="active",
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )

                # 检查通道是否已存在
                existing = self.supabase_client.select_data(
                    "video_channels", filters={"channel_id": channel.channel_id}
                )

                if existing:
                    # 更新现有通道
                    channel.updated_at = datetime.now()
                    self.supabase_client.update_data(
                        "video_channels",
                        channel.to_dict(),
                        {"channel_id": channel.channel_id},
                    )
                    updated += 1
                    logger.debug(f"✅ 更新通道: {channel.channel_name}")
                else:
                    # 插入新通道
                    self.supabase_client.insert_data(
                        "video_channels", channel.to_dict()
                    )
                    inserted += 1
                    logger.debug(f"✅ 插入通道: {channel.channel_name}")

            except Exception as e:
                logger.error(f"❌ 同步通道 {channel_info.channel_name} 失败: {e}")

        logger.info(f"✅ 视频通道同步完成，插入: {inserted}, 更新: {updated}")
        return {"inserted": inserted, "updated": updated}

    def _update_sync_log_error(self, sync_id: str, error_message: str) -> None:
        """
        更新同步日志错误信息

        Args:
            sync_id: 同步日志ID
            error_message: 错误信息
        """
        try:
            self.supabase_client.update_data(
                "sync_logs",
                {
                    "sync_status": "failed",
                    "end_time": datetime.now().isoformat(),
                    "error_message": error_message,
                },
                {"id": sync_id},
            )
        except Exception as e:
            logger.error(f"❌ 更新同步日志失败: {e}")

    def get_sync_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取同步历史记录

        Args:
            limit: 返回记录数量限制

        Returns:
            同步历史记录列表
        """
        try:
            records = self.supabase_client.select_data(
                "sync_logs", columns="*", limit=limit
            )
            return records
        except Exception as e:
            logger.error(f"❌ 获取同步历史失败: {e}")
            return []

    def get_sync_status(self) -> Dict[str, Any]:
        """
        获取同步状态信息

        Returns:
            同步状态信息
        """
        try:
            # 获取最近的同步记录
            recent_syncs = self.supabase_client.select_data(
                "sync_logs", columns="*", limit=5
            )

            # 获取数据统计
            org_count = len(self.supabase_client.select_data("organizations"))
            channel_count = len(self.supabase_client.select_data("video_channels"))

            return {
                "organizations_count": org_count,
                "channels_count": channel_count,
                "recent_syncs": recent_syncs,
                "last_sync": recent_syncs[0] if recent_syncs else None,
            }
        except Exception as e:
            logger.error(f"❌ 获取同步状态失败: {e}")
            return {
                "organizations_count": 0,
                "channels_count": 0,
                "recent_syncs": [],
                "last_sync": None,
                "error": str(e),
            }
