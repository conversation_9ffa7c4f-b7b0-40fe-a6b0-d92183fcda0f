-- 验证数据库表结构脚本
-- 检查所有必需的表和列是否存在

-- 检查所有表是否存在
SELECT 
    'organizations' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'organizations') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_channels' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_channels') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_monitoring_points' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_monitoring_points') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'sync_logs' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'sync_logs') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'api_access_logs' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'api_access_logs') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_stream_sessions' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_stream_sessions') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_events' as table_name,
    CASE WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_events') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status;

-- 检查关键列是否存在
SELECT 
    'organizations.channel_id' as column_check,
    CASE WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'org_id') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_channels.channel_id' as column_check,
    CASE WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_channels' AND column_name = 'channel_id') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_stream_sessions.channel_id' as column_check,
    CASE WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_stream_sessions' AND column_name = 'channel_id') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status
UNION ALL
SELECT 
    'video_events.channel_id' as column_check,
    CASE WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_events' AND column_name = 'channel_id') 
         THEN '✅ 存在' ELSE '❌ 不存在' END as status;

-- 显示所有表的列信息
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN (
    'organizations', 
    'video_channels', 
    'video_monitoring_points', 
    'sync_logs', 
    'api_access_logs', 
    'video_stream_sessions', 
    'video_events'
)
ORDER BY table_name, ordinal_position;
