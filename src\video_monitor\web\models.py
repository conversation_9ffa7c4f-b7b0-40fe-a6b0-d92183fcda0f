"""
Web API数据模型

定义API请求和响应的数据模型
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(default=False, description="操作是否成功")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")


class OrganizationModel(BaseModel):
    """组织机构模型"""
    org_id: str = Field(description="组织ID")
    org_name: str = Field(description="组织名称")
    parent_id: Optional[str] = Field(default=None, description="父组织ID")
    level: int = Field(default=0, description="组织级别")
    node_type: int = Field(default=1, description="节点类型")
    type_code: str = Field(default="01", description="类型编码")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")


class VideoChannelModel(BaseModel):
    """视频通道模型"""
    channel_id: str = Field(description="通道ID")
    channel_name: str = Field(description="通道名称")
    org_id: str = Field(description="所属组织ID")
    org_name: str = Field(description="所属组织名称")
    org_level: int = Field(default=0, description="组织级别")
    device_type: Optional[str] = Field(default=None, description="设备类型")
    status: str = Field(default="active", description="通道状态")
    stream_url: Optional[str] = Field(default=None, description="主流地址")
    backup_stream_url: Optional[str] = Field(default=None, description="备用流地址")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")


class VideoStreamRequest(BaseModel):
    """视频流请求模型"""
    channel_id: str = Field(description="通道ID")
    protocol: Optional[str] = Field(default="HLS", description="流协议")
    quality: Optional[str] = Field(default="high", description="视频质量")


class VideoStreamResponse(BaseResponse):
    """视频流响应模型"""
    data: Optional[Dict[str, Any]] = Field(default=None, description="视频流信息")
    
    class VideoStreamData(BaseModel):
        """视频流数据"""
        channel_id: str = Field(description="通道ID")
        channel_name: str = Field(description="通道名称")
        stream_url: str = Field(description="流地址")
        backup_stream_url: Optional[str] = Field(default=None, description="备用流地址")
        protocol: str = Field(description="流协议")
        session_token: Optional[str] = Field(default=None, description="会话令牌")
        expires_at: Optional[datetime] = Field(default=None, description="过期时间")


class OrganizationListResponse(BaseResponse):
    """组织机构列表响应"""
    data: List[OrganizationModel] = Field(default=[], description="组织机构列表")
    total: int = Field(default=0, description="总数量")


class VideoChannelListResponse(BaseResponse):
    """视频通道列表响应"""
    data: List[VideoChannelModel] = Field(default=[], description="视频通道列表")
    total: int = Field(default=0, description="总数量")


class SyncRequest(BaseModel):
    """数据同步请求模型"""
    sync_type: str = Field(default="manual", description="同步类型")
    force: bool = Field(default=False, description="是否强制同步")


class SyncResponse(BaseResponse):
    """数据同步响应模型"""
    data: Optional[Dict[str, Any]] = Field(default=None, description="同步结果")
    
    class SyncData(BaseModel):
        """同步数据"""
        sync_id: str = Field(description="同步ID")
        organizations: Dict[str, int] = Field(description="组织机构同步统计")
        channels: Dict[str, int] = Field(description="视频通道同步统计")
        duration: str = Field(description="同步耗时")


class SyncStatusResponse(BaseResponse):
    """同步状态响应模型"""
    data: Optional[Dict[str, Any]] = Field(default=None, description="同步状态信息")


class HealthCheckResponse(BaseResponse):
    """健康检查响应模型"""
    data: Optional[Dict[str, Any]] = Field(default=None, description="系统状态信息")
    
    class HealthData(BaseModel):
        """健康检查数据"""
        database: str = Field(description="数据库状态")
        auth_service: str = Field(description="认证服务状态")
        sync_service: str = Field(description="同步服务状态")
        scheduler: str = Field(description="调度器状态")
        uptime: str = Field(description="运行时间")


class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size


class FilterParams(BaseModel):
    """过滤参数模型"""
    org_id: Optional[str] = Field(default=None, description="组织ID过滤")
    status: Optional[str] = Field(default=None, description="状态过滤")
    device_type: Optional[str] = Field(default=None, description="设备类型过滤")
    search: Optional[str] = Field(default=None, description="搜索关键词")


class ApiAccessLogModel(BaseModel):
    """API访问日志模型"""
    id: str = Field(description="日志ID")
    endpoint: str = Field(description="接口地址")
    method: str = Field(description="请求方法")
    status_code: int = Field(description="响应状态码")
    response_time_ms: Optional[int] = Field(default=None, description="响应时间(毫秒)")
    user_agent: Optional[str] = Field(default=None, description="用户代理")
    ip_address: Optional[str] = Field(default=None, description="IP地址")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    created_at: datetime = Field(description="创建时间")


class ApiAccessLogListResponse(BaseResponse):
    """API访问日志列表响应"""
    data: List[ApiAccessLogModel] = Field(default=[], description="访问日志列表")
    total: int = Field(default=0, description="总数量")
