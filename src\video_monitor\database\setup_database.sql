-- 海事系统视频数据后端服务数据库设置脚本
-- 分步骤安全创建所有数据库对象

-- ==========================================
-- 第一步：创建基础表（无外键依赖）
-- ==========================================

-- 组织机构表
CREATE TABLE IF NOT EXISTS organizations (
  org_id VARCHAR(255) PRIMARY KEY,
  org_name VARCHAR(255) NOT NULL,
  parent_id VARCHAR(255),
  level INTEGER DEFAULT 0,
  node_type INTEGER DEFAULT 1,
  type_code VARCHAR(10) DEFAULT '01',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sync_type VARCHAR(50) DEFAULT 'manual',
  sync_status VARCHAR(50) DEFAULT 'running',
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  organizations_count INTEGER DEFAULT 0,
  channels_count INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API访问日志表
CREATE TABLE IF NOT EXISTS api_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) DEFAULT 'GET',
  status_code INTEGER DEFAULT 200,
  response_time_ms INTEGER,
  user_agent TEXT,
  ip_address INET,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- 第二步：创建视频相关表
-- ==========================================

-- 视频通道表
CREATE TABLE IF NOT EXISTS video_channels (
  channel_id VARCHAR(255) PRIMARY KEY,
  channel_name VARCHAR(255) NOT NULL,
  org_id VARCHAR(255) NOT NULL,
  org_name VARCHAR(255) NOT NULL,
  org_level INTEGER DEFAULT 0,
  device_type VARCHAR(100),
  status VARCHAR(50) DEFAULT 'active',
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频监控点表
CREATE TABLE IF NOT EXISTS video_monitoring_points (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  point_name VARCHAR(255) NOT NULL,
  description TEXT,
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  location_name VARCHAR(255),
  camera_type VARCHAR(100),
  resolution VARCHAR(50),
  viewing_angle INTEGER,
  night_vision BOOLEAN DEFAULT FALSE,
  ptz_control BOOLEAN DEFAULT FALSE,
  monitoring_area VARCHAR(255),
  installation_date DATE,
  maintenance_contact VARCHAR(255),
  point_status VARCHAR(50) DEFAULT 'active',
  device_ip INET,
  device_port INTEGER DEFAULT 80,
  channel_code VARCHAR(100),
  device_username VARCHAR(100),
  device_password_encrypted TEXT,
  device_status VARCHAR(50) DEFAULT 'offline',
  device_type VARCHAR(50) DEFAULT 'generic',
  last_heartbeat TIMESTAMP WITH TIME ZONE,
  device_info JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- 第三步：创建会话和事件表
-- ==========================================

-- 视频流会话表
CREATE TABLE IF NOT EXISTS video_stream_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  stream_url VARCHAR(500) NOT NULL,
  protocol VARCHAR(50) DEFAULT 'HLS',
  user_id VARCHAR(255),
  session_token VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_accessed TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0
);

-- 视频事件记录表
CREATE TABLE IF NOT EXISTS video_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL,
  event_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity VARCHAR(50) DEFAULT 'info',
  description TEXT,
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE,
  processed_by VARCHAR(255),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- 第四步：创建更新时间触发器函数
-- ==========================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ==========================================
-- 第五步：创建更新时间触发器
-- ==========================================

DROP TRIGGER IF EXISTS update_organizations_updated_at ON organizations;
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE
    ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_video_channels_updated_at ON video_channels;
CREATE TRIGGER update_video_channels_updated_at BEFORE UPDATE
    ON video_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_video_monitoring_points_updated_at ON video_monitoring_points;
CREATE TRIGGER update_video_monitoring_points_updated_at BEFORE UPDATE
    ON video_monitoring_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==========================================
-- 第六步：启用行级安全性
-- ==========================================

ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_monitoring_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_stream_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_events ENABLE ROW LEVEL SECURITY;

-- ==========================================
-- 第七步：创建策略（允许所有操作）
-- ==========================================

DROP POLICY IF EXISTS "Allow all operations on organizations" ON organizations;
CREATE POLICY "Allow all operations on organizations" ON organizations
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on video_channels" ON video_channels;
CREATE POLICY "Allow all operations on video_channels" ON video_channels
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on video_monitoring_points" ON video_monitoring_points;
CREATE POLICY "Allow all operations on video_monitoring_points" ON video_monitoring_points
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on sync_logs" ON sync_logs;
CREATE POLICY "Allow all operations on sync_logs" ON sync_logs
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on api_access_logs" ON api_access_logs;
CREATE POLICY "Allow all operations on api_access_logs" ON api_access_logs
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on video_stream_sessions" ON video_stream_sessions;
CREATE POLICY "Allow all operations on video_stream_sessions" ON video_stream_sessions
    FOR ALL USING (true) WITH CHECK (true);

DROP POLICY IF EXISTS "Allow all operations on video_events" ON video_events;
CREATE POLICY "Allow all operations on video_events" ON video_events
    FOR ALL USING (true) WITH CHECK (true);

-- ==========================================
-- 完成提示
-- ==========================================

DO $$
BEGIN
    RAISE NOTICE '✅ 数据库表结构创建完成！';
    RAISE NOTICE '📋 请运行 verify_tables.sql 验证表结构';
    RAISE NOTICE '📊 然后运行 create_indexes_safely.sql 创建索引';
END
$$;
