#!/usr/bin/env python3
"""
测试Supabase连接和创建数据库表
"""

import sys
import os
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from video_monitor.database.supabase_client import get_supabase_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_connection():
    """测试Supabase连接"""
    logger.info("🔍 开始测试Supabase连接...")
    
    try:
        # 获取Supabase客户端
        supabase_client = get_supabase_client()
        
        # 连接到Supabase
        if supabase_client.connect():
            logger.info("✅ Supabase连接成功")
            
            # 测试连接
            if supabase_client.test_connection():
                logger.info("✅ Supabase连接测试通过")
                return True
            else:
                logger.warning("⚠️ Supabase连接测试失败，但连接已建立")
                return True
        else:
            logger.error("❌ Supabase连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试Supabase连接时发生异常: {e}")
        return False


def create_tables():
    """创建数据库表"""
    logger.info("🔧 开始创建数据库表...")
    
    try:
        # 读取SQL文件
        sql_file_path = os.path.join(os.path.dirname(__file__), 'src', 'video_monitor', 'database', 'maritime_video_system.sql')
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        logger.info(f"📄 已读取SQL文件: {sql_file_path}")
        
        # 获取Supabase客户端
        supabase_client = get_supabase_client()
        
        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        success_count = 0
        error_count = 0
        
        for i, statement in enumerate(sql_statements):
            if not statement:
                continue
                
            try:
                # 跳过注释行
                if statement.startswith('--') or statement.startswith('/*'):
                    continue
                
                logger.debug(f"执行SQL语句 {i+1}/{len(sql_statements)}: {statement[:50]}...")
                
                # 使用原生SQL执行
                # 注意：Supabase可能不支持所有PostgreSQL功能，我们需要直接使用客户端
                result = supabase_client.client.postgrest.session.post(
                    f"{supabase_client.url}/rest/v1/rpc/exec_sql",
                    json={"sql": statement},
                    headers={"apikey": supabase_client.key}
                )
                
                if result.status_code == 200:
                    success_count += 1
                else:
                    logger.warning(f"⚠️ SQL语句执行警告: {result.text}")
                    error_count += 1
                    
            except Exception as e:
                logger.warning(f"⚠️ 执行SQL语句时出现警告: {e}")
                error_count += 1
        
        logger.info(f"✅ 数据库表创建完成，成功: {success_count}, 警告: {error_count}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表时发生异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始Supabase连接和数据库初始化测试")
    
    # 测试连接
    if not test_connection():
        logger.error("❌ Supabase连接测试失败，退出")
        return False
    
    # 创建表（可选，因为Supabase可能有限制）
    logger.info("ℹ️ 注意：由于Supabase的限制，可能无法直接执行DDL语句")
    logger.info("ℹ️ 请手动在Supabase控制台中执行SQL脚本创建表结构")
    
    # 测试基本操作
    try:
        supabase_client = get_supabase_client()
        
        # 尝试查询现有表
        logger.info("🔍 尝试查询现有表...")
        
        # 测试查询organizations表
        try:
            result = supabase_client.select_data('organizations', limit=1)
            logger.info(f"✅ organizations表查询成功，返回 {len(result)} 条记录")
        except Exception as e:
            logger.info(f"ℹ️ organizations表可能不存在: {e}")
        
        # 测试查询video_channels表
        try:
            result = supabase_client.select_data('video_channels', limit=1)
            logger.info(f"✅ video_channels表查询成功，返回 {len(result)} 条记录")
        except Exception as e:
            logger.info(f"ℹ️ video_channels表可能不存在: {e}")
        
        logger.info("✅ Supabase基本功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试基本操作时发生异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        logger.info("🎉 所有测试完成")
        sys.exit(0)
    else:
        logger.error("💥 测试失败")
        sys.exit(1)
