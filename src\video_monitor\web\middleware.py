"""
Web API中间件

提供日志记录、访问统计等中间件功能
"""

import time
import logging
from typing import Callable
from datetime import datetime

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..database.models import ApiAccessLog

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志
        
        Args:
            request: HTTP请求
            call_next: 下一个处理器
            
        Returns:
            HTTP响应
        """
        start_time = time.time()
        
        # 记录请求信息
        logger.info(
            f"📥 {request.method} {request.url.path} - "
            f"Client: {request.client.host if request.client else 'unknown'}"
        )
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"📤 {request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s"
            )
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录异常
            process_time = time.time() - start_time
            logger.error(
                f"❌ {request.method} {request.url.path} - "
                f"Error: {str(e)} - "
                f"Time: {process_time:.3f}s"
            )
            raise


class ApiAccessLogMiddleware(BaseHTTPMiddleware):
    """API访问日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录访问日志到数据库
        
        Args:
            request: HTTP请求
            call_next: 下一个处理器
            
        Returns:
            HTTP响应
        """
        start_time = time.time()
        
        # 获取请求信息
        endpoint = str(request.url.path)
        method = request.method
        user_agent = request.headers.get("user-agent")
        ip_address = self._get_client_ip(request)
        
        response = None
        error_message = None
        
        try:
            # 处理请求
            response = await call_next(request)
            
        except Exception as e:
            error_message = str(e)
            # 创建错误响应
            response = Response(
                content=f"Internal Server Error: {error_message}",
                status_code=500
            )
        
        finally:
            # 计算响应时间
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # 记录访问日志（异步，不阻塞响应）
            try:
                await self._log_access(
                    endpoint=endpoint,
                    method=method,
                    status_code=response.status_code if response else 500,
                    response_time_ms=response_time_ms,
                    user_agent=user_agent,
                    ip_address=ip_address,
                    error_message=error_message
                )
            except Exception as log_error:
                logger.error(f"记录访问日志失败: {log_error}")
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求
            
        Returns:
            客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 使用直接连接的IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    async def _log_access(self, endpoint: str, method: str, status_code: int,
                         response_time_ms: int, user_agent: str = None,
                         ip_address: str = None, error_message: str = None):
        """
        记录访问日志到数据库
        
        Args:
            endpoint: 接口地址
            method: 请求方法
            status_code: 响应状态码
            response_time_ms: 响应时间（毫秒）
            user_agent: 用户代理
            ip_address: IP地址
            error_message: 错误信息
        """
        try:
            # 导入这里避免循环导入
            from .app import get_app_service
            
            supabase_client = get_app_service("supabase_client")
            
            # 创建访问日志记录
            access_log = ApiAccessLog(
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                response_time_ms=response_time_ms,
                user_agent=user_agent,
                ip_address=ip_address,
                error_message=error_message,
                created_at=datetime.now()
            )
            
            # 插入数据库
            supabase_client.insert_data("api_access_logs", access_log.to_dict())
            
        except Exception as e:
            # 记录日志失败不应该影响正常请求
            logger.error(f"插入访问日志失败: {e}")


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app, calls_per_minute: int = 60):
        """
        初始化速率限制中间件
        
        Args:
            app: ASGI应用
            calls_per_minute: 每分钟允许的请求数
        """
        super().__init__(app)
        self.calls_per_minute = calls_per_minute
        self.client_requests = {}  # 存储客户端请求记录
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并检查速率限制
        
        Args:
            request: HTTP请求
            call_next: 下一个处理器
            
        Returns:
            HTTP响应
        """
        client_ip = self._get_client_ip(request)
        current_time = time.time()
        
        # 清理过期记录
        self._cleanup_expired_records(current_time)
        
        # 检查客户端请求记录
        if client_ip not in self.client_requests:
            self.client_requests[client_ip] = []
        
        client_record = self.client_requests[client_ip]
        
        # 过滤最近一分钟的请求
        recent_requests = [
            req_time for req_time in client_record
            if current_time - req_time < 60
        ]
        
        # 检查是否超过限制
        if len(recent_requests) >= self.calls_per_minute:
            logger.warning(f"客户端 {client_ip} 超过速率限制")
            return Response(
                content="Rate limit exceeded",
                status_code=429,
                headers={"Retry-After": "60"}
            )
        
        # 记录当前请求
        recent_requests.append(current_time)
        self.client_requests[client_ip] = recent_requests
        
        # 处理请求
        response = await call_next(request)
        
        # 添加速率限制头
        response.headers["X-RateLimit-Limit"] = str(self.calls_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(
            self.calls_per_minute - len(recent_requests)
        )
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        if request.client:
            return request.client.host
        return "unknown"
    
    def _cleanup_expired_records(self, current_time: float):
        """清理过期的请求记录"""
        for client_ip in list(self.client_requests.keys()):
            # 过滤最近一分钟的请求
            recent_requests = [
                req_time for req_time in self.client_requests[client_ip]
                if current_time - req_time < 60
            ]
            
            if recent_requests:
                self.client_requests[client_ip] = recent_requests
            else:
                # 删除没有最近请求的客户端记录
                del self.client_requests[client_ip]


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        添加安全响应头
        
        Args:
            request: HTTP请求
            call_next: 下一个处理器
            
        Returns:
            HTTP响应
        """
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response
