-- 海事系统视频数据后端服务数据库表
-- 基于原有video_resources.sql，针对海事系统进行优化

-- 组织机构表
CREATE TABLE IF NOT EXISTS organizations (
  org_id VARCHAR(255) PRIMARY KEY,
  org_name VARCHAR(255) NOT NULL,
  parent_id VARCHAR(255),
  level INTEGER DEFAULT 0,
  node_type INTEGER DEFAULT 1,
  type_code VARCHAR(10) DEFAULT '01',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  FOREIGN KEY (parent_id) REFERENCES organizations(org_id) ON DELETE CASCADE
);

-- 视频通道表
CREATE TABLE IF NOT EXISTS video_channels (
  channel_id VARCHAR(255) PRIMARY KEY,
  channel_name VARCHAR(255) NOT NULL,
  org_id VARCHAR(255) NOT NULL,
  org_name VARCHAR(255) NOT NULL,
  org_level INTEGER DEFAULT 0,
  device_type VARCHAR(100),
  status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE
);

-- 视频监控点表（扩展版本，包含设备信息）
CREATE TABLE IF NOT EXISTS video_monitoring_points (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  point_name VARCHAR(255) NOT NULL,
  description TEXT,
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  location_name VARCHAR(255),
  camera_type VARCHAR(100),
  resolution VARCHAR(50),
  viewing_angle INTEGER,
  night_vision BOOLEAN DEFAULT FALSE,
  ptz_control BOOLEAN DEFAULT FALSE,
  monitoring_area VARCHAR(255),
  installation_date DATE,
  maintenance_contact VARCHAR(255),
  point_status VARCHAR(50) DEFAULT 'active' CHECK (point_status IN ('active', 'inactive', 'maintenance')),
  
  -- 设备连接信息
  device_ip INET,
  device_port INTEGER DEFAULT 80,
  channel_code VARCHAR(100),
  device_username VARCHAR(100),
  device_password_encrypted TEXT,
  device_status VARCHAR(50) DEFAULT 'offline' CHECK (device_status IN ('online', 'offline', 'error')),
  device_type VARCHAR(50) DEFAULT 'generic',
  last_heartbeat TIMESTAMP WITH TIME ZONE,
  device_info JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sync_type VARCHAR(50) DEFAULT 'manual' CHECK (sync_type IN ('manual', 'scheduled')),
  sync_status VARCHAR(50) DEFAULT 'running' CHECK (sync_status IN ('running', 'success', 'failed')),
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  organizations_count INTEGER DEFAULT 0,
  channels_count INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API访问日志表
CREATE TABLE IF NOT EXISTS api_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) DEFAULT 'GET',
  status_code INTEGER DEFAULT 200,
  response_time_ms INTEGER,
  user_agent TEXT,
  ip_address INET,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频流会话表
CREATE TABLE IF NOT EXISTS video_stream_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  stream_url VARCHAR(500) NOT NULL,
  protocol VARCHAR(50) DEFAULT 'HLS',
  user_id VARCHAR(255),
  session_token VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_accessed TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0
);

-- 视频事件记录表
CREATE TABLE IF NOT EXISTS video_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL, -- motion_detected, alarm_triggered, device_offline, etc.
  event_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity VARCHAR(50) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  description TEXT,
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE,
  processed_by VARCHAR(255),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加表和列的注释
COMMENT ON TABLE organizations IS '组织机构表';
COMMENT ON COLUMN organizations.org_id IS '组织ID';
COMMENT ON COLUMN organizations.org_name IS '组织名称';
COMMENT ON COLUMN organizations.parent_id IS '父组织ID';
COMMENT ON COLUMN organizations.level IS '组织级别';
COMMENT ON COLUMN organizations.node_type IS '节点类型';
COMMENT ON COLUMN organizations.type_code IS '类型编码';

COMMENT ON TABLE video_channels IS '视频通道表';
COMMENT ON COLUMN video_channels.channel_id IS '通道ID';
COMMENT ON COLUMN video_channels.channel_name IS '通道名称';
COMMENT ON COLUMN video_channels.org_id IS '所属组织ID';
COMMENT ON COLUMN video_channels.org_name IS '所属组织名称';
COMMENT ON COLUMN video_channels.org_level IS '组织级别';
COMMENT ON COLUMN video_channels.device_type IS '设备类型';
COMMENT ON COLUMN video_channels.status IS '通道状态';
COMMENT ON COLUMN video_channels.stream_url IS '主流地址';
COMMENT ON COLUMN video_channels.backup_stream_url IS '备用流地址';

COMMENT ON TABLE video_monitoring_points IS '视频监控点表';
COMMENT ON TABLE sync_logs IS '数据同步日志表';
COMMENT ON TABLE api_access_logs IS 'API访问日志表';
COMMENT ON TABLE video_stream_sessions IS '视频流会话表';
COMMENT ON TABLE video_events IS '视频事件记录表';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_organizations_parent_id ON organizations(parent_id);
CREATE INDEX IF NOT EXISTS idx_organizations_level ON organizations(level);

CREATE INDEX IF NOT EXISTS idx_video_channels_org_id ON video_channels(org_id);
CREATE INDEX IF NOT EXISTS idx_video_channels_status ON video_channels(status);
CREATE INDEX IF NOT EXISTS idx_video_channels_device_type ON video_channels(device_type);

CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_location ON video_monitoring_points(location_lat, location_lng);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_status ON video_monitoring_points(point_status);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_device_status ON video_monitoring_points(device_status);

CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_type ON sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_status ON sync_logs(sync_status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_start_time ON sync_logs(start_time);

CREATE INDEX IF NOT EXISTS idx_api_access_logs_endpoint ON api_access_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_created_at ON api_access_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_status_code ON api_access_logs(status_code);

CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_channel_id ON video_stream_sessions(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_expires_at ON video_stream_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_is_active ON video_stream_sessions(is_active);

CREATE INDEX IF NOT EXISTS idx_video_events_channel_id ON video_events(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_events_event_time ON video_events(event_time);
CREATE INDEX IF NOT EXISTS idx_video_events_event_type ON video_events(event_type);
CREATE INDEX IF NOT EXISTS idx_video_events_severity ON video_events(severity);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建更新时间触发器
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE
    ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_channels_updated_at BEFORE UPDATE
    ON video_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_monitoring_points_updated_at BEFORE UPDATE
    ON video_monitoring_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全性 (RLS)
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_monitoring_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_stream_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_events ENABLE ROW LEVEL SECURITY;

-- 创建策略允许所有操作（在生产环境中应该根据实际需求调整）
CREATE POLICY "Allow all operations on organizations" ON organizations
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_channels" ON video_channels
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_monitoring_points" ON video_monitoring_points
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on sync_logs" ON sync_logs
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on api_access_logs" ON api_access_logs
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_stream_sessions" ON video_stream_sessions
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_events" ON video_events
    FOR ALL USING (true) WITH CHECK (true);

-- 添加外键约束（在所有表创建完成后）
ALTER TABLE video_stream_sessions
ADD CONSTRAINT fk_video_stream_sessions_channel_id
FOREIGN KEY (channel_id) REFERENCES video_channels(channel_id) ON DELETE CASCADE;

ALTER TABLE video_events
ADD CONSTRAINT fk_video_events_channel_id
FOREIGN KEY (channel_id) REFERENCES video_channels(channel_id) ON DELETE CASCADE;
