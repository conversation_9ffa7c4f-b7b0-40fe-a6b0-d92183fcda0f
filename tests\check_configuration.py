#!/usr/bin/env python3
"""
配置检查脚本

检查系统配置和依赖是否正确
"""

import sys
import logging
import importlib
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_python_version():
    """检查Python版本"""
    logger.info("🔍 检查Python版本...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        logger.info(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        logger.error(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro} (需要 >= 3.8)")
        return False


def check_required_packages():
    """检查必需的Python包"""
    logger.info("🔍 检查必需的Python包...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'requests',
        'supabase',
        'apscheduler',
        'python-multipart'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            logger.info(f"✅ {package}")
        except ImportError:
            logger.error(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ 缺少以下包: {', '.join(missing_packages)}")
        logger.info("💡 安装命令: pip install " + " ".join(missing_packages))
        return False
    
    logger.info("✅ 所有必需包已安装")
    return True


def check_project_structure():
    """检查项目结构"""
    logger.info("🔍 检查项目结构...")
    
    required_files = [
        'src/video_monitor/config/settings.py',
        'src/video_monitor/database/supabase_client.py',
        'src/video_monitor/sync/sync_service.py',
        'src/video_monitor/web/app.py',
        'src/video_monitor/web/models.py',
        'src/video_monitor/web/middleware.py',
        'src/video_monitor/web/routes/__init__.py',
        'src/video_monitor/web/routes/video_streams.py',
        'src/video_monitor/web/routes/organizations.py',
        'src/video_monitor/web/routes/video_channels.py',
        'src/video_monitor/web/routes/sync.py',
        'src/video_monitor/web/routes/logs.py',
        'src/video_monitor/run_web_server.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            logger.info(f"✅ {file_path}")
        else:
            logger.error(f"❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少以下文件: {len(missing_files)} 个")
        return False
    
    logger.info("✅ 项目结构完整")
    return True


def check_configuration():
    """检查配置文件"""
    logger.info("🔍 检查配置...")
    
    try:
        from src.video_monitor.config.settings import Settings
        
        # 尝试初始化设置
        settings = Settings()
        
        # 检查关键配置
        if hasattr(settings, 'supabase_url') and settings.supabase_url:
            logger.info(f"✅ Supabase URL: {settings.supabase_url}")
        else:
            logger.error("❌ Supabase URL 未配置")
            return False
        
        if hasattr(settings, 'supabase_key') and settings.supabase_key:
            logger.info("✅ Supabase Key: 已配置")
        else:
            logger.error("❌ Supabase Key 未配置")
            return False
        
        logger.info("✅ 配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False


def check_database_connection():
    """检查数据库连接"""
    logger.info("🔍 检查数据库连接...")
    
    try:
        from src.video_monitor.database.supabase_client import SupabaseClient
        from src.video_monitor.config.settings import Settings
        
        settings = Settings()
        client = SupabaseClient(settings)
        
        # 尝试连接测试
        result = client.test_connection()
        
        if result:
            logger.info("✅ 数据库连接正常")
            return True
        else:
            logger.error("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据库连接检查异常: {e}")
        return False


def check_web_modules():
    """检查Web模块"""
    logger.info("🔍 检查Web模块...")
    
    try:
        # 检查主要模块是否可以导入
        from src.video_monitor.web.app import create_app
        from src.video_monitor.web.models import BaseResponse
        from src.video_monitor.web.middleware import LoggingMiddleware
        
        logger.info("✅ Web模块导入成功")
        
        # 尝试创建应用
        app = create_app()
        if app:
            logger.info("✅ FastAPI应用创建成功")
            return True
        else:
            logger.error("❌ FastAPI应用创建失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ Web模块检查异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始系统配置检查")
    logger.info(f"📁 项目根目录: {project_root}")
    
    checks = [
        ("Python版本", check_python_version),
        ("必需包", check_required_packages),
        ("项目结构", check_project_structure),
        ("配置文件", check_configuration),
        ("数据库连接", check_database_connection),
        ("Web模块", check_web_modules),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 检查项目: {check_name}")
        logger.info(f"{'='*50}")
        
        try:
            if check_func():
                passed += 1
                logger.info(f"✅ {check_name} 检查通过")
            else:
                logger.error(f"❌ {check_name} 检查失败")
        except Exception as e:
            logger.error(f"❌ {check_name} 检查异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"📊 检查结果汇总")
    logger.info(f"{'='*50}")
    logger.info(f"✅ 通过: {passed}/{total}")
    logger.info(f"❌ 失败: {total - passed}/{total}")
    logger.info(f"📈 成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 所有检查通过！系统配置正确")
        logger.info("💡 可以启动Web服务器: python src/video_monitor/run_web_server.py")
        return True
    else:
        logger.error("💥 部分检查失败！请修复问题后重试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
