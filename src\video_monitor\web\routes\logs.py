"""
日志管理API路由

提供API访问日志查询等功能
"""

import logging
from typing import Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Query

from ..models import (
    ApiAccessLogListResponse, BaseResponse
)
from ..app import get_app_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/access", response_model=ApiAccessLogListResponse)
async def get_access_logs(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    endpoint: Optional[str] = Query(default=None, description="接口地址过滤"),
    method: Optional[str] = Query(default=None, description="请求方法过滤"),
    status_code: Optional[int] = Query(default=None, description="状态码过滤"),
    start_date: Optional[str] = Query(default=None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(default=None, description="结束日期 (YYYY-MM-DD)")
):
    """
    获取API访问日志
    
    支持分页、过滤和日期范围查询
    
    Args:
        page: 页码
        page_size: 每页数量
        endpoint: 接口地址过滤
        method: 请求方法过滤
        status_code: 状态码过滤
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        API访问日志列表响应
    """
    try:
        logger.info(f"获取API访问日志: page={page}, page_size={page_size}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 构建过滤条件
        filters = {}
        if endpoint:
            filters["endpoint"] = endpoint
        if method:
            filters["method"] = method.upper()
        if status_code:
            filters["status_code"] = status_code
        
        # 处理日期过滤
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                filters["created_at__gte"] = start_datetime.isoformat()
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="开始日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                filters["created_at__lt"] = end_datetime.isoformat()
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="结束日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询数据
        logs = supabase_client.query_data(
            "api_access_logs",
            filters=filters,
            limit=page_size,
            offset=offset,
            order_by="created_at DESC"
        )
        
        # 查询总数
        total = supabase_client.count_data("api_access_logs", filters=filters)
        
        # 转换为模型
        from ..models import ApiAccessLogModel
        log_models = [ApiAccessLogModel(**log) for log in logs]
        
        return ApiAccessLogListResponse(
            success=True,
            message="获取API访问日志成功",
            data=log_models,
            total=total
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API访问日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取API访问日志失败: {str(e)}"
        )


@router.get("/access/stats", response_model=dict)
async def get_access_stats(
    days: int = Query(default=7, ge=1, le=30, description="统计天数")
):
    """
    获取API访问统计
    
    Args:
        days: 统计天数
        
    Returns:
        访问统计信息
    """
    try:
        logger.info(f"获取API访问统计: days={days}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询指定时间范围内的日志
        logs = supabase_client.query_data(
            "api_access_logs",
            filters={
                "created_at__gte": start_date.isoformat(),
                "created_at__lt": end_date.isoformat()
            }
        )
        
        # 统计分析
        stats = {
            "total_requests": len(logs),
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d"),
                "days": days
            },
            "status_codes": {},
            "endpoints": {},
            "methods": {},
            "response_times": {
                "avg": 0,
                "min": 0,
                "max": 0
            },
            "error_rate": 0
        }
        
        if logs:
            # 状态码统计
            for log in logs:
                status = log.get("status_code", 0)
                stats["status_codes"][status] = stats["status_codes"].get(status, 0) + 1
            
            # 接口统计
            for log in logs:
                endpoint = log.get("endpoint", "unknown")
                stats["endpoints"][endpoint] = stats["endpoints"].get(endpoint, 0) + 1
            
            # 方法统计
            for log in logs:
                method = log.get("method", "unknown")
                stats["methods"][method] = stats["methods"].get(method, 0) + 1
            
            # 响应时间统计
            response_times = [log.get("response_time_ms", 0) for log in logs if log.get("response_time_ms")]
            if response_times:
                stats["response_times"]["avg"] = sum(response_times) / len(response_times)
                stats["response_times"]["min"] = min(response_times)
                stats["response_times"]["max"] = max(response_times)
            
            # 错误率计算
            error_count = sum(1 for log in logs if log.get("status_code", 200) >= 400)
            stats["error_rate"] = (error_count / len(logs)) * 100 if logs else 0
        
        return {
            "success": True,
            "message": "获取API访问统计成功",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取API访问统计失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取API访问统计失败: {str(e)}"
        )


@router.delete("/access", response_model=BaseResponse)
async def clear_access_logs(
    keep_days: int = Query(default=30, ge=1, description="保留天数")
):
    """
    清理API访问日志
    
    删除指定天数之前的访问日志
    
    Args:
        keep_days: 保留天数
        
    Returns:
        清理结果
    """
    try:
        logger.info(f"清理API访问日志: keep_days={keep_days}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 计算截止日期
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        # 删除旧日志
        deleted_count = supabase_client.delete_data(
            "api_access_logs",
            filters={"created_at__lt": cutoff_date.isoformat()}
        )
        
        return BaseResponse(
            success=True,
            message=f"清理完成，删除了 {deleted_count} 条访问日志"
        )
        
    except Exception as e:
        logger.error(f"清理API访问日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"清理API访问日志失败: {str(e)}"
        )


@router.get("/errors", response_model=dict)
async def get_error_logs(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    days: int = Query(default=7, ge=1, le=30, description="查询天数")
):
    """
    获取错误日志
    
    查询指定天数内的错误请求日志
    
    Args:
        page: 页码
        page_size: 每页数量
        days: 查询天数
        
    Returns:
        错误日志列表
    """
    try:
        logger.info(f"获取错误日志: page={page}, page_size={page_size}, days={days}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 计算时间范围
        start_date = datetime.now() - timedelta(days=days)
        
        # 构建过滤条件（状态码 >= 400 表示错误）
        filters = {
            "status_code__gte": 400,
            "created_at__gte": start_date.isoformat()
        }
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询错误日志
        error_logs = supabase_client.query_data(
            "api_access_logs",
            filters=filters,
            limit=page_size,
            offset=offset,
            order_by="created_at DESC"
        )
        
        # 查询总数
        total = supabase_client.count_data("api_access_logs", filters=filters)
        
        return {
            "success": True,
            "message": "获取错误日志成功",
            "data": error_logs,
            "total": total,
            "page": page,
            "page_size": page_size,
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "days": days
            }
        }
        
    except Exception as e:
        logger.error(f"获取错误日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取错误日志失败: {str(e)}"
        )
