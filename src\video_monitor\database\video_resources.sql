-- 视频资源管理系统数据库表
-- 基于emergency_reports.sql的结构，为视频应用创建相应的表

-- 视频监控点表（扩展版本，包含设备信息）
CREATE TABLE IF NOT EXISTS video_monitoring_points (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  point_name VARCHAR(255) NOT NULL,
  description TEXT,
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  location_lat DECIMAL(10, 8) NOT NULL,
  location_lng DECIMAL(11, 8) NOT NULL,
  location_name VARCHAR(255),
  camera_type VARCHAR(100),
  resolution VARCHAR(50),
  viewing_angle INTEGER,
  night_vision BOOLEAN DEFAULT FALSE,
  ptz_control BOOLEAN DEFAULT FALSE,
  monitoring_area VARCHAR(255),
  installation_date DATE,
  maintenance_contact VARCHAR(255),
  point_status VARCHAR(50) DEFAULT 'active' CHECK (point_status IN ('active', 'inactive', 'maintenance')),
  
  -- 设备连接信息
  device_ip INET,
  device_port INTEGER DEFAULT 80,
  channel_code VARCHAR(100),
  device_username VARCHAR(100),
  device_password_encrypted TEXT,
  device_status VARCHAR(50) DEFAULT 'offline' CHECK (device_status IN ('online', 'offline', 'error')),
  device_type VARCHAR(50) DEFAULT 'generic',
  last_heartbeat TIMESTAMP WITH TIME ZONE,
  device_info JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频录像记录表
CREATE TABLE IF NOT EXISTS video_recordings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT,
  duration INTEGER, -- 录像时长（秒）
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  recording_type VARCHAR(50) DEFAULT 'manual' CHECK (recording_type IN ('manual', 'scheduled', 'motion', 'alarm')),
  quality VARCHAR(50),
  thumbnail_path VARCHAR(500),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频流会话表
CREATE TABLE IF NOT EXISTS video_stream_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  stream_url VARCHAR(500) NOT NULL,
  protocol VARCHAR(50) DEFAULT 'HLS',
  user_id VARCHAR(255),
  session_token VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_accessed TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0
);

-- 设备心跳日志表
CREATE TABLE IF NOT EXISTS device_heartbeat_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL,
  response_time_ms INTEGER,
  error_message TEXT,
  device_info JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频事件记录表
CREATE TABLE IF NOT EXISTS video_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  event_type VARCHAR(100) NOT NULL, -- motion_detected, alarm_triggered, device_offline, etc.
  event_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity VARCHAR(50) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  description TEXT,
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE,
  processed_by VARCHAR(255),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频预设位置表（用于PTZ摄像头）
CREATE TABLE IF NOT EXISTS video_preset_positions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  preset_name VARCHAR(255) NOT NULL,
  preset_number INTEGER,
  pan_position DECIMAL(8, 4),
  tilt_position DECIMAL(8, 4),
  zoom_level DECIMAL(6, 2),
  description TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频质量监控表
CREATE TABLE IF NOT EXISTS video_quality_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_point_id UUID REFERENCES video_monitoring_points(id) ON DELETE CASCADE,
  metric_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  frame_rate DECIMAL(6, 2),
  bitrate INTEGER,
  packet_loss_rate DECIMAL(5, 4),
  latency_ms INTEGER,
  resolution_actual VARCHAR(50),
  quality_score INTEGER CHECK (quality_score BETWEEN 0 AND 100),
  issues JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加表和列的注释
COMMENT ON TABLE video_monitoring_points IS '视频监控点表';
COMMENT ON COLUMN video_monitoring_points.point_name IS '监控点名称';
COMMENT ON COLUMN video_monitoring_points.description IS '监控点描述';
COMMENT ON COLUMN video_monitoring_points.stream_url IS '主视频流地址';
COMMENT ON COLUMN video_monitoring_points.backup_stream_url IS '备用视频流地址';
COMMENT ON COLUMN video_monitoring_points.location_lat IS '监控点纬度';
COMMENT ON COLUMN video_monitoring_points.location_lng IS '监控点经度';
COMMENT ON COLUMN video_monitoring_points.location_name IS '位置名称';
COMMENT ON COLUMN video_monitoring_points.camera_type IS '摄像头类型';
COMMENT ON COLUMN video_monitoring_points.resolution IS '分辨率';
COMMENT ON COLUMN video_monitoring_points.viewing_angle IS '视角角度';
COMMENT ON COLUMN video_monitoring_points.night_vision IS '是否支持夜视';
COMMENT ON COLUMN video_monitoring_points.ptz_control IS '是否支持PTZ控制';
COMMENT ON COLUMN video_monitoring_points.monitoring_area IS '监控区域';
COMMENT ON COLUMN video_monitoring_points.installation_date IS '安装日期';
COMMENT ON COLUMN video_monitoring_points.maintenance_contact IS '维护联系人';
COMMENT ON COLUMN video_monitoring_points.point_status IS '监控点状态';
COMMENT ON COLUMN video_monitoring_points.device_ip IS '设备IP地址';
COMMENT ON COLUMN video_monitoring_points.device_port IS '设备端口';
COMMENT ON COLUMN video_monitoring_points.channel_code IS '通道编码';
COMMENT ON COLUMN video_monitoring_points.device_username IS '设备用户名';
COMMENT ON COLUMN video_monitoring_points.device_password_encrypted IS '设备密码（加密）';
COMMENT ON COLUMN video_monitoring_points.device_status IS '设备状态';
COMMENT ON COLUMN video_monitoring_points.device_type IS '设备类型';
COMMENT ON COLUMN video_monitoring_points.last_heartbeat IS '最后心跳时间';
COMMENT ON COLUMN video_monitoring_points.device_info IS '设备信息（JSON）';

COMMENT ON TABLE video_recordings IS '视频录像记录表';
COMMENT ON COLUMN video_recordings.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN video_recordings.file_name IS '录像文件名';
COMMENT ON COLUMN video_recordings.file_path IS '录像文件路径';
COMMENT ON COLUMN video_recordings.file_size IS '文件大小（字节）';
COMMENT ON COLUMN video_recordings.duration IS '录像时长（秒）';
COMMENT ON COLUMN video_recordings.start_time IS '录像开始时间';
COMMENT ON COLUMN video_recordings.end_time IS '录像结束时间';
COMMENT ON COLUMN video_recordings.recording_type IS '录像类型';
COMMENT ON COLUMN video_recordings.quality IS '录像质量';
COMMENT ON COLUMN video_recordings.thumbnail_path IS '缩略图路径';
COMMENT ON COLUMN video_recordings.metadata IS '录像元数据';

COMMENT ON TABLE video_stream_sessions IS '视频流会话表';
COMMENT ON COLUMN video_stream_sessions.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN video_stream_sessions.stream_url IS '流地址';
COMMENT ON COLUMN video_stream_sessions.protocol IS '流协议';
COMMENT ON COLUMN video_stream_sessions.user_id IS '用户ID';
COMMENT ON COLUMN video_stream_sessions.session_token IS '会话令牌';
COMMENT ON COLUMN video_stream_sessions.expires_at IS '过期时间';
COMMENT ON COLUMN video_stream_sessions.is_active IS '是否活跃';
COMMENT ON COLUMN video_stream_sessions.last_accessed IS '最后访问时间';
COMMENT ON COLUMN video_stream_sessions.access_count IS '访问次数';

COMMENT ON TABLE device_heartbeat_logs IS '设备心跳日志表';
COMMENT ON COLUMN device_heartbeat_logs.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN device_heartbeat_logs.status IS '设备状态';
COMMENT ON COLUMN device_heartbeat_logs.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN device_heartbeat_logs.error_message IS '错误信息';
COMMENT ON COLUMN device_heartbeat_logs.device_info IS '设备信息';

COMMENT ON TABLE video_events IS '视频事件记录表';
COMMENT ON COLUMN video_events.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN video_events.event_type IS '事件类型';
COMMENT ON COLUMN video_events.event_time IS '事件时间';
COMMENT ON COLUMN video_events.severity IS '严重程度';
COMMENT ON COLUMN video_events.description IS '事件描述';
COMMENT ON COLUMN video_events.metadata IS '事件元数据';
COMMENT ON COLUMN video_events.is_processed IS '是否已处理';
COMMENT ON COLUMN video_events.processed_by IS '处理人';
COMMENT ON COLUMN video_events.processed_at IS '处理时间';

COMMENT ON TABLE video_preset_positions IS '视频预设位置表';
COMMENT ON COLUMN video_preset_positions.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN video_preset_positions.preset_name IS '预设位置名称';
COMMENT ON COLUMN video_preset_positions.preset_number IS '预设位置编号';
COMMENT ON COLUMN video_preset_positions.pan_position IS '水平位置';
COMMENT ON COLUMN video_preset_positions.tilt_position IS '垂直位置';
COMMENT ON COLUMN video_preset_positions.zoom_level IS '缩放级别';
COMMENT ON COLUMN video_preset_positions.description IS '位置描述';
COMMENT ON COLUMN video_preset_positions.is_default IS '是否为默认位置';

COMMENT ON TABLE video_quality_metrics IS '视频质量监控表';
COMMENT ON COLUMN video_quality_metrics.monitoring_point_id IS '监控点ID';
COMMENT ON COLUMN video_quality_metrics.metric_time IS '监控时间';
COMMENT ON COLUMN video_quality_metrics.frame_rate IS '帧率';
COMMENT ON COLUMN video_quality_metrics.bitrate IS '比特率';
COMMENT ON COLUMN video_quality_metrics.packet_loss_rate IS '丢包率';
COMMENT ON COLUMN video_quality_metrics.latency_ms IS '延迟（毫秒）';
COMMENT ON COLUMN video_quality_metrics.resolution_actual IS '实际分辨率';
COMMENT ON COLUMN video_quality_metrics.quality_score IS '质量评分';
COMMENT ON COLUMN video_quality_metrics.issues IS '质量问题';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_location ON video_monitoring_points(location_lat, location_lng);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_status ON video_monitoring_points(point_status);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_device_status ON video_monitoring_points(device_status);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_monitoring_area ON video_monitoring_points(monitoring_area);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_device_type ON video_monitoring_points(device_type);

CREATE INDEX IF NOT EXISTS idx_video_recordings_monitoring_point_id ON video_recordings(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_video_recordings_start_time ON video_recordings(start_time);
CREATE INDEX IF NOT EXISTS idx_video_recordings_recording_type ON video_recordings(recording_type);

CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_monitoring_point_id ON video_stream_sessions(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_user_id ON video_stream_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_expires_at ON video_stream_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_is_active ON video_stream_sessions(is_active);

CREATE INDEX IF NOT EXISTS idx_device_heartbeat_logs_monitoring_point_id ON device_heartbeat_logs(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_device_heartbeat_logs_created_at ON device_heartbeat_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_device_heartbeat_logs_status ON device_heartbeat_logs(status);

CREATE INDEX IF NOT EXISTS idx_video_events_monitoring_point_id ON video_events(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_video_events_event_time ON video_events(event_time);
CREATE INDEX IF NOT EXISTS idx_video_events_event_type ON video_events(event_type);
CREATE INDEX IF NOT EXISTS idx_video_events_severity ON video_events(severity);
CREATE INDEX IF NOT EXISTS idx_video_events_is_processed ON video_events(is_processed);

CREATE INDEX IF NOT EXISTS idx_video_preset_positions_monitoring_point_id ON video_preset_positions(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_video_preset_positions_preset_number ON video_preset_positions(preset_number);

CREATE INDEX IF NOT EXISTS idx_video_quality_metrics_monitoring_point_id ON video_quality_metrics(monitoring_point_id);
CREATE INDEX IF NOT EXISTS idx_video_quality_metrics_metric_time ON video_quality_metrics(metric_time);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建更新时间触发器
CREATE TRIGGER update_video_monitoring_points_updated_at BEFORE UPDATE
    ON video_monitoring_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_recordings_updated_at BEFORE UPDATE
    ON video_recordings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_preset_positions_updated_at BEFORE UPDATE
    ON video_preset_positions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全性 (RLS)
ALTER TABLE video_monitoring_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recordings ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_stream_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_heartbeat_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_preset_positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_quality_metrics ENABLE ROW LEVEL SECURITY;

-- 创建策略允许所有操作（在生产环境中应该根据实际需求调整）
CREATE POLICY "Allow all operations on video_monitoring_points" ON video_monitoring_points
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_recordings" ON video_recordings
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_stream_sessions" ON video_stream_sessions
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on device_heartbeat_logs" ON device_heartbeat_logs
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_events" ON video_events
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_preset_positions" ON video_preset_positions
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_quality_metrics" ON video_quality_metrics
    FOR ALL USING (true) WITH CHECK (true);

-- 插入示例数据
INSERT INTO video_monitoring_points (
    point_name,
    description,
    stream_url,
    backup_stream_url,
    location_lat,
    location_lng,
    location_name,
    camera_type,
    resolution,
    viewing_angle,
    night_vision,
    ptz_control,
    monitoring_area,
    installation_date,
    maintenance_contact,
    point_status,
    device_ip,
    device_port,
    channel_code,
    device_username,
    device_type
) VALUES 
(
    '青岛港1号码头监控',
    '青岛港1号码头主要作业区域监控',
    'rtsp://*************:554/live/ch1',
    'rtsp://*************:554/live/ch1',
    36.066667,
    120.383333,
    '青岛港1号码头',
    '高清网络摄像头',
    '1920x1080',
    120,
    true,
    true,
    '码头作业区',
    '2024-01-15',
    '张三 13800138001',
    'active',
    '*************',
    80,
    'channel001',
    'admin',
    'dahua'
),
(
    '大连港主航道监控',
    '大连港主航道船舶进出港监控',
    'rtsp://*************:554/live/ch1',
    'rtsp://*************:554/live/ch1',
    38.914003,
    121.618622,
    '大连港主航道',
    '云台摄像头',
    '1920x1080',
    360,
    true,
    true,
    '主航道',
    '2024-02-20',
    '李四 13800138002',
    'active',
    '*************',
    80,
    'channel002',
    'admin',
    'dahua'
),
(
    '上海港客运码头监控',
    '上海港客运码头安全监控',
    'rtsp://192.168.3.100:554/live/ch1',
    null,
    31.230416,
    121.473701,
    '上海港客运码头',
    '红外夜视摄像头',
    '1280x720',
    90,
    true,
    false,
    '客运码头',
    '2024-03-10',
    '王五 13800138003',
    'active',
    '192.168.3.100',
    80,
    'channel003',
    'admin',
    'generic'
),
(
    '天津港危险品区域监控',
    '天津港危险品存储区域安全监控',
    'rtsp://192.168.4.100:554/live/ch1',
    'rtsp://192.168.4.101:554/live/ch1',
    39.021467,
    117.721611,
    '天津港危险品区域',
    '防爆摄像头',
    '1920x1080',
    180,
    true,
    true,
    '危险品区域',
    '2024-04-05',
    '赵六 13800138004',
    'active',
    '192.168.4.100',
    80,
    'channel004',
    'admin',
    'dahua'
),
(
    '宁波港锚地监控',
    '宁波港锚地船舶停泊监控',
    'rtsp://*************:554/live/ch1',
    null,
    29.868388,
    121.543333,
    '宁波港锚地',
    '球机摄像头',
    '1920x1080',
    360,
    true,
    true,
    '锚地区域',
    '2024-05-12',
    '孙七 13800138005',
    'maintenance',
    '*************',
    80,
    'channel005',
    'admin',
    'dahua'
);

-- 为监控点插入预设位置
INSERT INTO video_preset_positions (
    monitoring_point_id,
    preset_name,
    preset_number,
    pan_position,
    tilt_position,
    zoom_level,
    description,
    is_default
) VALUES 
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '青岛港1号码头监控'),
    '码头全景',
    1,
    0.0000,
    0.0000,
    1.00,
    '码头作业区全景视图',
    true
),
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '青岛港1号码头监控'),
    '起重机作业区',
    2,
    45.0000,
    -15.0000,
    3.50,
    '起重机作业区域特写',
    false
),
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '大连港主航道监控'),
    '航道入口',
    1,
    90.0000,
    0.0000,
    2.00,
    '主航道入口监控',
    true
),
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '大连港主航道监控'),
    '航道出口',
    2,
    270.0000,
    0.0000,
    2.00,
    '主航道出口监控',
    false
);

-- 插入示例录像记录
INSERT INTO video_recordings (
    monitoring_point_id,
    file_name,
    file_path,
    file_size,
    duration,
    start_time,
    end_time,
    recording_type,
    quality,
    thumbnail_path
) VALUES 
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '青岛港1号码头监控'),
    'qingdao_port_20240629_100000.mp4',
    '/recordings/2024/06/29/qingdao_port_20240629_100000.mp4',
    1048576000,
    3600,
    '2024-06-29 10:00:00+08',
    '2024-06-29 11:00:00+08',
    'scheduled',
    'high',
    '/thumbnails/qingdao_port_20240629_100000.jpg'
),
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '大连港主航道监控'),
    'dalian_port_20240629_140000.mp4',
    '/recordings/2024/06/29/dalian_port_20240629_140000.mp4',
    2097152000,
    7200,
    '2024-06-29 14:00:00+08',
    '2024-06-29 16:00:00+08',
    'motion',
    'high',
    '/thumbnails/dalian_port_20240629_140000.jpg'
);

-- 插入示例事件记录
INSERT INTO video_events (
    monitoring_point_id,
    event_type,
    event_time,
    severity,
    description,
    metadata
) VALUES 
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '青岛港1号码头监控'),
    'motion_detected',
    '2024-06-29 10:30:00+08',
    'info',
    '检测到码头作业区域有移动物体',
    '{"confidence": 0.95, "object_type": "vehicle", "area": "loading_zone"}'::jsonb
),
(
    (SELECT id FROM video_monitoring_points WHERE point_name = '宁波港锚地监控'),
    'device_offline',
    '2024-06-29 15:45:00+08',
    'warning',
    '设备离线，可能是网络连接问题',
    '{"last_response": "2024-06-29T15:40:00Z", "error_code": "NETWORK_TIMEOUT"}'::jsonb
);

-- 创建视图：监控点状态统计
CREATE OR REPLACE VIEW video_monitoring_stats AS
SELECT 
    COUNT(*) as total_points,
    COUNT(CASE WHEN point_status = 'active' THEN 1 END) as active_points,
    COUNT(CASE WHEN point_status = 'inactive' THEN 1 END) as inactive_points,
    COUNT(CASE WHEN point_status = 'maintenance' THEN 1 END) as maintenance_points,
    COUNT(CASE WHEN device_status = 'online' THEN 1 END) as online_devices,
    COUNT(CASE WHEN device_status = 'offline' THEN 1 END) as offline_devices,
    COUNT(CASE WHEN device_status = 'error' THEN 1 END) as error_devices,
    COUNT(CASE WHEN ptz_control = true THEN 1 END) as ptz_enabled_points,
    COUNT(CASE WHEN night_vision = true THEN 1 END) as night_vision_points
FROM video_monitoring_points;

-- 创建函数：获取监控点详细信息
CREATE OR REPLACE FUNCTION get_monitoring_point_details(point_id UUID)
RETURNS TABLE (
    point_info JSONB,
    recent_events JSONB,
    recording_stats JSONB,
    quality_stats JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(vmp.*) as point_info,
        (
            SELECT jsonb_agg(to_jsonb(ve.*))
            FROM video_events ve
            WHERE ve.monitoring_point_id = point_id
            AND ve.created_at >= NOW() - INTERVAL '24 hours'
            ORDER BY ve.created_at DESC
            LIMIT 10
        ) as recent_events,
        (
            SELECT jsonb_build_object(
                'total_recordings', COUNT(*),
                'total_duration', SUM(duration),
                'total_size', SUM(file_size),
                'last_recording', MAX(end_time)
            )
            FROM video_recordings vr
            WHERE vr.monitoring_point_id = point_id
        ) as recording_stats,
        (
            SELECT jsonb_build_object(
                'avg_quality_score', AVG(quality_score),
                'avg_frame_rate', AVG(frame_rate),
                'avg_latency', AVG(latency_ms),
                'last_check', MAX(metric_time)
            )
            FROM video_quality_metrics vqm
            WHERE vqm.monitoring_point_id = point_id
            AND vqm.created_at >= NOW() - INTERVAL '24 hours'
        ) as quality_stats
    FROM video_monitoring_points vmp
    WHERE vmp.id = point_id;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：清理过期的流会话
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM video_stream_sessions
    WHERE expires_at < NOW() OR (is_active = false AND last_accessed < NOW() - INTERVAL '1 hour');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取设备健康状态
CREATE OR REPLACE FUNCTION get_device_health_status()
RETURNS TABLE (
    monitoring_point_id UUID,
    point_name VARCHAR(255),
    device_status VARCHAR(50),
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    avg_response_time DECIMAL,
    error_count INTEGER,
    health_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vmp.id as monitoring_point_id,
        vmp.point_name,
        vmp.device_status,
        vmp.last_heartbeat,
        COALESCE(AVG(dhl.response_time_ms), 0) as avg_response_time,
        COUNT(CASE WHEN dhl.status = 'error' THEN 1 END)::INTEGER as error_count,
        CASE 
            WHEN vmp.device_status = 'online' THEN 
                GREATEST(0, 100 - (COUNT(CASE WHEN dhl.status = 'error' THEN 1 END) * 10))
            WHEN vmp.device_status = 'offline' THEN 0
            ELSE 25
        END::INTEGER as health_score
    FROM video_monitoring_points vmp
    LEFT JOIN device_heartbeat_logs dhl ON vmp.id = dhl.monitoring_point_id
        AND dhl.created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY vmp.id, vmp.point_name, vmp.device_status, vmp.last_heartbeat
    ORDER BY health_score DESC, vmp.point_name;
END;
$$ LANGUAGE plpgsql;
