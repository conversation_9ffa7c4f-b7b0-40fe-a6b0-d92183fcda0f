"""
组织机构API路由

提供组织机构查询等功能
"""

import logging
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Query, Depends

from ..models import (
    OrganizationModel, OrganizationListResponse, ErrorResponse,
    PaginationParams, FilterParams
)
from ..app import get_app_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=OrganizationListResponse)
async def get_organizations(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    org_id: Optional[str] = Query(default=None, description="组织ID过滤"),
    parent_id: Optional[str] = Query(default=None, description="父组织ID过滤"),
    level: Optional[int] = Query(default=None, description="组织级别过滤"),
    search: Optional[str] = Query(default=None, description="搜索关键词")
):
    """
    获取组织机构列表
    
    支持分页、过滤和搜索功能
    
    Args:
        page: 页码
        page_size: 每页数量
        org_id: 组织ID过滤
        parent_id: 父组织ID过滤
        level: 组织级别过滤
        search: 搜索关键词
        
    Returns:
        组织机构列表响应
    """
    try:
        logger.info(f"获取组织机构列表: page={page}, page_size={page_size}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 构建过滤条件
        filters = {}
        if org_id:
            filters["org_id"] = org_id
        if parent_id:
            filters["parent_id"] = parent_id
        if level is not None:
            filters["level"] = level
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询数据
        organizations = supabase_client.query_data(
            "organizations",
            filters=filters,
            search_fields=["org_name"] if search else None,
            search_term=search,
            limit=page_size,
            offset=offset,
            order_by="level, org_name"
        )
        
        # 查询总数
        total = supabase_client.count_data("organizations", filters=filters)
        
        # 转换为模型
        org_models = [OrganizationModel(**org) for org in organizations]
        
        return OrganizationListResponse(
            success=True,
            message="获取组织机构列表成功",
            data=org_models,
            total=total
        )
        
    except Exception as e:
        logger.error(f"获取组织机构列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取组织机构列表失败: {str(e)}"
        )


@router.get("/{org_id}", response_model=OrganizationModel)
async def get_organization(org_id: str):
    """
    获取单个组织机构信息
    
    Args:
        org_id: 组织ID
        
    Returns:
        组织机构信息
    """
    try:
        logger.info(f"获取组织机构信息: org_id={org_id}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 查询组织信息
        organizations = supabase_client.query_data(
            "organizations",
            filters={"org_id": org_id}
        )
        
        if not organizations:
            raise HTTPException(
                status_code=404,
                detail=f"组织机构 {org_id} 不存在"
            )
        
        return OrganizationModel(**organizations[0])
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取组织机构信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取组织机构信息失败: {str(e)}"
        )


@router.get("/{org_id}/children", response_model=OrganizationListResponse)
async def get_organization_children(
    org_id: str,
    recursive: bool = Query(default=False, description="是否递归获取所有子级")
):
    """
    获取组织机构的子级
    
    Args:
        org_id: 父组织ID
        recursive: 是否递归获取所有子级
        
    Returns:
        子级组织机构列表
    """
    try:
        logger.info(f"获取组织机构子级: org_id={org_id}, recursive={recursive}")
        
        supabase_client = get_app_service("supabase_client")
        
        if recursive:
            # 递归获取所有子级
            children = _get_all_children_recursive(supabase_client, org_id)
        else:
            # 只获取直接子级
            children = supabase_client.query_data(
                "organizations",
                filters={"parent_id": org_id},
                order_by="level, org_name"
            )
        
        # 转换为模型
        org_models = [OrganizationModel(**org) for org in children]
        
        return OrganizationListResponse(
            success=True,
            message="获取子级组织机构成功",
            data=org_models,
            total=len(org_models)
        )
        
    except Exception as e:
        logger.error(f"获取组织机构子级失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取组织机构子级失败: {str(e)}"
        )


def _get_all_children_recursive(supabase_client, parent_id: str) -> List[dict]:
    """
    递归获取所有子级组织机构
    
    Args:
        supabase_client: Supabase客户端
        parent_id: 父组织ID
        
    Returns:
        所有子级组织机构列表
    """
    all_children = []
    
    # 获取直接子级
    direct_children = supabase_client.query_data(
        "organizations",
        filters={"parent_id": parent_id},
        order_by="level, org_name"
    )
    
    for child in direct_children:
        all_children.append(child)
        # 递归获取子级的子级
        grandchildren = _get_all_children_recursive(supabase_client, child["org_id"])
        all_children.extend(grandchildren)
    
    return all_children
