"""
数据库模型

定义与Supabase数据库表对应的数据模型
"""

from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


@dataclass
class Organization:
    """组织机构模型"""
    org_id: str
    org_name: str
    parent_id: Optional[str] = None
    level: int = 0
    node_type: int = 1
    type_code: str = "01"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Organization':
        """从字典创建实例"""
        # 处理datetime字段
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        if 'updated_at' in data and data['updated_at']:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
        return cls(**data)


@dataclass
class VideoChannel:
    """视频通道模型"""
    channel_id: str
    channel_name: str
    org_id: str
    org_name: str
    org_level: int = 0
    device_type: Optional[str] = None
    status: str = "active"
    stream_url: Optional[str] = None
    backup_stream_url: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VideoChannel':
        """从字典创建实例"""
        # 处理datetime字段
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        if 'updated_at' in data and data['updated_at']:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
        return cls(**data)


@dataclass
class VideoMonitoringPoint:
    """视频监控点模型"""
    id: Optional[str] = None
    point_name: str = ""
    description: Optional[str] = None
    stream_url: Optional[str] = None
    backup_stream_url: Optional[str] = None
    location_lat: Optional[float] = None
    location_lng: Optional[float] = None
    location_name: Optional[str] = None
    camera_type: Optional[str] = None
    resolution: Optional[str] = None
    viewing_angle: Optional[int] = None
    night_vision: bool = False
    ptz_control: bool = False
    monitoring_area: Optional[str] = None
    installation_date: Optional[str] = None
    maintenance_contact: Optional[str] = None
    point_status: str = "active"
    device_ip: Optional[str] = None
    device_port: int = 80
    channel_code: Optional[str] = None
    device_username: Optional[str] = None
    device_password_encrypted: Optional[str] = None
    device_status: str = "offline"
    device_type: str = "generic"
    last_heartbeat: Optional[datetime] = None
    device_info: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.id is None:
            self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        if self.last_heartbeat:
            data['last_heartbeat'] = self.last_heartbeat.isoformat()
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VideoMonitoringPoint':
        """从字典创建实例"""
        # 处理datetime字段
        if 'last_heartbeat' in data and data['last_heartbeat']:
            data['last_heartbeat'] = datetime.fromisoformat(data['last_heartbeat'].replace('Z', '+00:00'))
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        if 'updated_at' in data and data['updated_at']:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
        return cls(**data)


@dataclass
class SyncLog:
    """同步日志模型"""
    id: Optional[str] = None
    sync_type: str = "manual"  # manual, scheduled
    sync_status: str = "running"  # running, success, failed
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    organizations_count: int = 0
    channels_count: int = 0
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.id is None:
            self.id = str(uuid.uuid4())
        if self.start_time is None:
            self.start_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        if self.start_time:
            data['start_time'] = self.start_time.isoformat()
        if self.end_time:
            data['end_time'] = self.end_time.isoformat()
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncLog':
        """从字典创建实例"""
        # 处理datetime字段
        if 'start_time' in data and data['start_time']:
            data['start_time'] = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        if 'end_time' in data and data['end_time']:
            data['end_time'] = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        return cls(**data)


@dataclass
class ApiAccessLog:
    """API访问日志模型"""
    id: Optional[str] = None
    endpoint: str = ""
    method: str = "GET"
    status_code: int = 200
    response_time_ms: Optional[int] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.id is None:
            self.id = str(uuid.uuid4())
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApiAccessLog':
        """从字典创建实例"""
        # 处理datetime字段
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        return cls(**data)
