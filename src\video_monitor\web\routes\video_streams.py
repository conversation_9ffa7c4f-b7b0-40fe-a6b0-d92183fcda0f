"""
视频流API路由

提供视频流URI查询等功能
"""

import logging
from typing import Optional
from datetime import datetime, timedelta
import uuid

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse

from ..models import (
    VideoStreamRequest, VideoStreamResponse, ErrorResponse,
    BaseResponse
)
from ..app import get_app_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/uri/{channel_id}", response_model=VideoStreamResponse)
async def get_video_stream_uri(
    channel_id: str,
    protocol: str = Query(default="HLS", description="流协议 (HLS/RTMP/WebRTC)"),
    quality: str = Query(default="high", description="视频质量 (high/medium/low)"),
    session_duration: int = Query(default=3600, description="会话持续时间(秒)")
):
    """
    获取视频流URI地址
    
    根据通道ID获取对应的视频流地址，支持不同协议和质量
    
    Args:
        channel_id: 视频通道ID
        protocol: 流协议类型
        quality: 视频质量
        session_duration: 会话持续时间
        
    Returns:
        包含视频流信息的响应
    """
    try:
        logger.info(f"获取视频流URI: channel_id={channel_id}, protocol={protocol}, quality={quality}")
        
        # 获取服务实例
        supabase_client = get_app_service("supabase_client")
        monitor_service = get_app_service("monitor_service")
        
        # 查询视频通道信息
        channel_data = supabase_client.query_data(
            "video_channels",
            filters={"channel_id": channel_id}
        )
        
        if not channel_data:
            raise HTTPException(
                status_code=404,
                detail=f"视频通道 {channel_id} 不存在"
            )
        
        channel = channel_data[0]
        
        # 检查通道状态
        if channel.get("status") != "active":
            raise HTTPException(
                status_code=400,
                detail=f"视频通道 {channel_id} 状态异常: {channel.get('status')}"
            )
        
        # 获取流地址
        stream_url = channel.get("stream_url")
        backup_stream_url = channel.get("backup_stream_url")
        
        if not stream_url:
            # 尝试从监控服务获取实时流地址
            try:
                stream_info = monitor_service.get_video_stream_info(channel_id)
                if stream_info and stream_info.get("stream_url"):
                    stream_url = stream_info["stream_url"]
                    backup_stream_url = stream_info.get("backup_stream_url")
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"无法获取通道 {channel_id} 的流地址"
                    )
            except Exception as e:
                logger.error(f"从监控服务获取流地址失败: {e}")
                raise HTTPException(
                    status_code=503,
                    detail="监控服务不可用，无法获取流地址"
                )
        
        # 根据协议转换流地址
        final_stream_url = _convert_stream_url(stream_url, protocol, quality)
        final_backup_url = _convert_stream_url(backup_stream_url, protocol, quality) if backup_stream_url else None
        
        # 创建视频流会话
        session_token = str(uuid.uuid4())
        expires_at = datetime.now() + timedelta(seconds=session_duration)
        
        session_data = {
            "channel_id": channel_id,
            "stream_url": final_stream_url,
            "protocol": protocol,
            "session_token": session_token,
            "created_at": datetime.now().isoformat(),
            "expires_at": expires_at.isoformat(),
            "is_active": True,
            "access_count": 0
        }
        
        # 保存会话到数据库
        try:
            supabase_client.insert_data("video_stream_sessions", session_data)
        except Exception as e:
            logger.warning(f"保存视频流会话失败: {e}")
            # 会话保存失败不影响流地址返回
        
        # 构造响应数据
        response_data = {
            "channel_id": channel_id,
            "channel_name": channel.get("channel_name", ""),
            "stream_url": final_stream_url,
            "backup_stream_url": final_backup_url,
            "protocol": protocol,
            "session_token": session_token,
            "expires_at": expires_at
        }
        
        return VideoStreamResponse(
            success=True,
            message="获取视频流URI成功",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取视频流URI失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取视频流URI失败: {str(e)}"
        )


@router.post("/session/refresh", response_model=VideoStreamResponse)
async def refresh_video_stream_session(
    session_token: str,
    extend_duration: int = Query(default=3600, description="延长时间(秒)")
):
    """
    刷新视频流会话
    
    延长现有会话的有效期
    
    Args:
        session_token: 会话令牌
        extend_duration: 延长时间
        
    Returns:
        更新后的会话信息
    """
    try:
        logger.info(f"刷新视频流会话: session_token={session_token}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 查询会话信息
        session_data = supabase_client.query_data(
            "video_stream_sessions",
            filters={"session_token": session_token, "is_active": True}
        )
        
        if not session_data:
            raise HTTPException(
                status_code=404,
                detail="会话不存在或已过期"
            )
        
        session = session_data[0]
        
        # 检查会话是否过期
        expires_at = datetime.fromisoformat(session["expires_at"].replace("Z", "+00:00"))
        if expires_at < datetime.now():
            raise HTTPException(
                status_code=410,
                detail="会话已过期"
            )
        
        # 更新过期时间
        new_expires_at = datetime.now() + timedelta(seconds=extend_duration)
        
        update_data = {
            "expires_at": new_expires_at.isoformat(),
            "last_accessed": datetime.now().isoformat(),
            "access_count": session.get("access_count", 0) + 1
        }
        
        supabase_client.update_data(
            "video_stream_sessions",
            update_data,
            filters={"session_token": session_token}
        )
        
        # 构造响应数据
        response_data = {
            "channel_id": session["channel_id"],
            "stream_url": session["stream_url"],
            "protocol": session["protocol"],
            "session_token": session_token,
            "expires_at": new_expires_at
        }
        
        return VideoStreamResponse(
            success=True,
            message="会话刷新成功",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新视频流会话失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"刷新会话失败: {str(e)}"
        )


@router.delete("/session/{session_token}", response_model=BaseResponse)
async def close_video_stream_session(session_token: str):
    """
    关闭视频流会话
    
    主动关闭指定的视频流会话
    
    Args:
        session_token: 会话令牌
        
    Returns:
        操作结果
    """
    try:
        logger.info(f"关闭视频流会话: session_token={session_token}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 更新会话状态
        update_data = {
            "is_active": False,
            "last_accessed": datetime.now().isoformat()
        }
        
        result = supabase_client.update_data(
            "video_stream_sessions",
            update_data,
            filters={"session_token": session_token}
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )
        
        return BaseResponse(
            success=True,
            message="会话关闭成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关闭视频流会话失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"关闭会话失败: {str(e)}"
        )


@router.get("/sessions/cleanup", response_model=BaseResponse)
async def cleanup_expired_sessions():
    """
    清理过期会话
    
    删除所有过期的视频流会话记录
    
    Returns:
        清理结果
    """
    try:
        logger.info("开始清理过期会话")
        
        supabase_client = get_app_service("supabase_client")
        
        # 查询过期会话
        current_time = datetime.now().isoformat()
        expired_sessions = supabase_client.query_data(
            "video_stream_sessions",
            filters={"expires_at__lt": current_time, "is_active": True}
        )
        
        if expired_sessions:
            # 批量更新过期会话状态
            for session in expired_sessions:
                supabase_client.update_data(
                    "video_stream_sessions",
                    {"is_active": False},
                    filters={"id": session["id"]}
                )
            
            logger.info(f"清理了 {len(expired_sessions)} 个过期会话")
        
        return BaseResponse(
            success=True,
            message=f"清理完成，共处理 {len(expired_sessions)} 个过期会话"
        )
        
    except Exception as e:
        logger.error(f"清理过期会话失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"清理失败: {str(e)}"
        )


def _convert_stream_url(original_url: str, protocol: str, quality: str) -> str:
    """
    根据协议和质量转换流地址
    
    Args:
        original_url: 原始流地址
        protocol: 目标协议
        quality: 视频质量
        
    Returns:
        转换后的流地址
    """
    if not original_url:
        return original_url
    
    # 这里可以根据实际需求实现流地址转换逻辑
    # 例如：
    # - HLS: 转换为 .m3u8 格式
    # - RTMP: 转换为 rtmp:// 格式
    # - WebRTC: 转换为 WebRTC 格式
    
    # 简单示例实现
    if protocol.upper() == "HLS":
        if not original_url.endswith(".m3u8"):
            # 假设可以通过添加参数来获取HLS流
            separator = "&" if "?" in original_url else "?"
            return f"{original_url}{separator}format=hls&quality={quality}"
    elif protocol.upper() == "RTMP":
        if not original_url.startswith("rtmp://"):
            # 转换为RTMP格式（这里需要根据实际情况实现）
            return original_url.replace("http://", "rtmp://").replace("https://", "rtmp://")
    
    # 添加质量参数
    if quality != "high":
        separator = "&" if "?" in original_url else "?"
        return f"{original_url}{separator}quality={quality}"
    
    return original_url
