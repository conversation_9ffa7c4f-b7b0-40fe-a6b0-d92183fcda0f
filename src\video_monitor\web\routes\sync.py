"""
数据同步API路由

提供数据同步控制和状态查询功能
"""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, BackgroundTasks

from ..models import (
    SyncRequest, SyncResponse, SyncStatusResponse, BaseResponse
)
from ..app import get_app_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/start", response_model=SyncResponse)
async def start_sync(
    background_tasks: BackgroundTasks,
    sync_request: SyncRequest = SyncRequest()
):
    """
    启动数据同步
    
    手动触发数据同步操作
    
    Args:
        background_tasks: 后台任务
        sync_request: 同步请求参数
        
    Returns:
        同步操作响应
    """
    try:
        logger.info(f"启动数据同步: type={sync_request.sync_type}, force={sync_request.force}")
        
        sync_service = get_app_service("sync_service")
        
        # 检查是否有正在进行的同步
        if not sync_request.force and sync_service.is_syncing():
            raise HTTPException(
                status_code=409,
                detail="已有同步任务正在进行中，请等待完成或使用强制同步"
            )
        
        # 在后台执行同步
        background_tasks.add_task(
            sync_service.sync_all_data,
            sync_request.sync_type
        )
        
        return SyncResponse(
            success=True,
            message="数据同步已启动",
            data={
                "sync_type": sync_request.sync_type,
                "status": "started",
                "message": "同步任务已在后台启动"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动数据同步失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"启动数据同步失败: {str(e)}"
        )


@router.get("/status", response_model=SyncStatusResponse)
async def get_sync_status():
    """
    获取同步状态
    
    Returns:
        当前同步状态信息
    """
    try:
        logger.info("获取同步状态")
        
        sync_service = get_app_service("sync_service")
        supabase_client = get_app_service("supabase_client")
        
        # 获取当前同步状态
        is_syncing = sync_service.is_syncing()
        
        # 获取最近的同步记录
        recent_logs = supabase_client.query_data(
            "sync_logs",
            limit=5,
            order_by="created_at DESC"
        )
        
        # 获取统计信息
        total_orgs = supabase_client.count_data("organizations")
        total_channels = supabase_client.count_data("video_channels")
        
        status_data = {
            "is_syncing": is_syncing,
            "statistics": {
                "total_organizations": total_orgs,
                "total_channels": total_channels
            },
            "recent_sync_logs": recent_logs
        }
        
        return SyncStatusResponse(
            success=True,
            message="获取同步状态成功",
            data=status_data
        )
        
    except Exception as e:
        logger.error(f"获取同步状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取同步状态失败: {str(e)}"
        )


@router.post("/organizations", response_model=SyncResponse)
async def sync_organizations(background_tasks: BackgroundTasks):
    """
    同步组织机构数据
    
    只同步组织机构信息
    
    Args:
        background_tasks: 后台任务
        
    Returns:
        同步操作响应
    """
    try:
        logger.info("启动组织机构同步")
        
        sync_service = get_app_service("sync_service")
        
        # 在后台执行组织机构同步
        background_tasks.add_task(
            sync_service.sync_organizations,
            "manual"
        )
        
        return SyncResponse(
            success=True,
            message="组织机构同步已启动",
            data={
                "sync_type": "organizations_only",
                "status": "started"
            }
        )
        
    except Exception as e:
        logger.error(f"启动组织机构同步失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"启动组织机构同步失败: {str(e)}"
        )


@router.post("/channels", response_model=SyncResponse)
async def sync_channels(background_tasks: BackgroundTasks):
    """
    同步视频通道数据
    
    只同步视频通道信息
    
    Args:
        background_tasks: 后台任务
        
    Returns:
        同步操作响应
    """
    try:
        logger.info("启动视频通道同步")
        
        sync_service = get_app_service("sync_service")
        
        # 在后台执行视频通道同步
        background_tasks.add_task(
            sync_service.sync_video_channels,
            "manual"
        )
        
        return SyncResponse(
            success=True,
            message="视频通道同步已启动",
            data={
                "sync_type": "channels_only",
                "status": "started"
            }
        )
        
    except Exception as e:
        logger.error(f"启动视频通道同步失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"启动视频通道同步失败: {str(e)}"
        )


@router.get("/logs", response_model=dict)
async def get_sync_logs(
    page: int = 1,
    page_size: int = 20,
    sync_type: Optional[str] = None,
    sync_status: Optional[str] = None
):
    """
    获取同步日志
    
    Args:
        page: 页码
        page_size: 每页数量
        sync_type: 同步类型过滤
        sync_status: 同步状态过滤
        
    Returns:
        同步日志列表
    """
    try:
        logger.info(f"获取同步日志: page={page}, page_size={page_size}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 构建过滤条件
        filters = {}
        if sync_type:
            filters["sync_type"] = sync_type
        if sync_status:
            filters["sync_status"] = sync_status
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询日志
        logs = supabase_client.query_data(
            "sync_logs",
            filters=filters,
            limit=page_size,
            offset=offset,
            order_by="created_at DESC"
        )
        
        # 查询总数
        total = supabase_client.count_data("sync_logs", filters=filters)
        
        return {
            "success": True,
            "message": "获取同步日志成功",
            "data": logs,
            "total": total,
            "page": page,
            "page_size": page_size
        }
        
    except Exception as e:
        logger.error(f"获取同步日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取同步日志失败: {str(e)}"
        )


@router.delete("/logs", response_model=BaseResponse)
async def clear_sync_logs(
    keep_days: int = 30
):
    """
    清理同步日志
    
    删除指定天数之前的同步日志
    
    Args:
        keep_days: 保留天数
        
    Returns:
        清理结果
    """
    try:
        logger.info(f"清理同步日志: keep_days={keep_days}")
        
        supabase_client = get_app_service("supabase_client")
        
        # 计算截止日期
        from datetime import datetime, timedelta
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        # 删除旧日志
        deleted_count = supabase_client.delete_data(
            "sync_logs",
            filters={"created_at__lt": cutoff_date.isoformat()}
        )
        
        return BaseResponse(
            success=True,
            message=f"清理完成，删除了 {deleted_count} 条日志记录"
        )
        
    except Exception as e:
        logger.error(f"清理同步日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"清理同步日志失败: {str(e)}"
        )
