-- 安全创建索引脚本
-- 在创建索引前检查表和列是否存在

-- 检查并创建 organizations 表的索引
DO $$
BEGIN
    -- 检查 organizations 表是否存在
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'organizations') THEN
        -- 创建 parent_id 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'parent_id') THEN
            CREATE INDEX IF NOT EXISTS idx_organizations_parent_id ON organizations(parent_id);
            RAISE NOTICE '✅ 创建索引: idx_organizations_parent_id';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: organizations.parent_id';
        END IF;
        
        -- 创建 level 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'level') THEN
            CREATE INDEX IF NOT EXISTS idx_organizations_level ON organizations(level);
            RAISE NOTICE '✅ 创建索引: idx_organizations_level';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: organizations.level';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: organizations';
    END IF;
END
$$;

-- 检查并创建 video_channels 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_channels') THEN
        -- 创建 org_id 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_channels' AND column_name = 'org_id') THEN
            CREATE INDEX IF NOT EXISTS idx_video_channels_org_id ON video_channels(org_id);
            RAISE NOTICE '✅ 创建索引: idx_video_channels_org_id';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: video_channels.org_id';
        END IF;
        
        -- 创建 status 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_channels' AND column_name = 'status') THEN
            CREATE INDEX IF NOT EXISTS idx_video_channels_status ON video_channels(status);
            RAISE NOTICE '✅ 创建索引: idx_video_channels_status';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: video_channels.status';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: video_channels';
    END IF;
END
$$;

-- 检查并创建 sync_logs 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'sync_logs') THEN
        -- 创建 sync_type 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'sync_logs' AND column_name = 'sync_type') THEN
            CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_type ON sync_logs(sync_type);
            RAISE NOTICE '✅ 创建索引: idx_sync_logs_sync_type';
        END IF;
        
        -- 创建 sync_status 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'sync_logs' AND column_name = 'sync_status') THEN
            CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_status ON sync_logs(sync_status);
            RAISE NOTICE '✅ 创建索引: idx_sync_logs_sync_status';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: sync_logs';
    END IF;
END
$$;

-- 检查并创建 api_access_logs 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'api_access_logs') THEN
        -- 创建 endpoint 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'api_access_logs' AND column_name = 'endpoint') THEN
            CREATE INDEX IF NOT EXISTS idx_api_access_logs_endpoint ON api_access_logs(endpoint);
            RAISE NOTICE '✅ 创建索引: idx_api_access_logs_endpoint';
        END IF;
        
        -- 创建 created_at 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'api_access_logs' AND column_name = 'created_at') THEN
            CREATE INDEX IF NOT EXISTS idx_api_access_logs_created_at ON api_access_logs(created_at);
            RAISE NOTICE '✅ 创建索引: idx_api_access_logs_created_at';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: api_access_logs';
    END IF;
END
$$;

-- 检查并创建 video_stream_sessions 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_stream_sessions') THEN
        -- 创建 channel_id 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_stream_sessions' AND column_name = 'channel_id') THEN
            CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_channel_id ON video_stream_sessions(channel_id);
            RAISE NOTICE '✅ 创建索引: idx_video_stream_sessions_channel_id';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: video_stream_sessions.channel_id';
        END IF;
        
        -- 创建 expires_at 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_stream_sessions' AND column_name = 'expires_at') THEN
            CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_expires_at ON video_stream_sessions(expires_at);
            RAISE NOTICE '✅ 创建索引: idx_video_stream_sessions_expires_at';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: video_stream_sessions';
    END IF;
END
$$;

-- 检查并创建 video_events 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_events') THEN
        -- 创建 channel_id 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_events' AND column_name = 'channel_id') THEN
            CREATE INDEX IF NOT EXISTS idx_video_events_channel_id ON video_events(channel_id);
            RAISE NOTICE '✅ 创建索引: idx_video_events_channel_id';
        ELSE
            RAISE NOTICE '⚠️ 列不存在: video_events.channel_id';
        END IF;
        
        -- 创建 event_time 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_events' AND column_name = 'event_time') THEN
            CREATE INDEX IF NOT EXISTS idx_video_events_event_time ON video_events(event_time);
            RAISE NOTICE '✅ 创建索引: idx_video_events_event_time';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: video_events';
    END IF;
END
$$;

-- 检查并创建 video_monitoring_points 表的索引
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'video_monitoring_points') THEN
        -- 创建位置索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_monitoring_points' AND column_name = 'location_lat') 
           AND EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_monitoring_points' AND column_name = 'location_lng') THEN
            CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_location ON video_monitoring_points(location_lat, location_lng);
            RAISE NOTICE '✅ 创建索引: idx_video_monitoring_points_location';
        END IF;
        
        -- 创建 point_status 索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'video_monitoring_points' AND column_name = 'point_status') THEN
            CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_status ON video_monitoring_points(point_status);
            RAISE NOTICE '✅ 创建索引: idx_video_monitoring_points_status';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ 表不存在: video_monitoring_points';
    END IF;
END
$$;
