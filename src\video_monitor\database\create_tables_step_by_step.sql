-- 海事系统视频数据后端服务数据库表
-- 分步骤创建，避免外键依赖问题

-- 第一步：创建组织机构表
CREATE TABLE IF NOT EXISTS organizations (
  org_id VARCHAR(255) PRIMARY KEY,
  org_name VARCHAR(255) NOT NULL,
  parent_id VARCHAR(255),
  level INTEGER DEFAULT 0,
  node_type INTEGER DEFAULT 1,
  type_code VARCHAR(10) DEFAULT '01',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第二步：创建视频通道表
CREATE TABLE IF NOT EXISTS video_channels (
  channel_id VARCHAR(255) PRIMARY KEY,
  channel_name VARCHAR(255) NOT NULL,
  org_id VARCHAR(255) NOT NULL,
  org_name VARCHAR(255) NOT NULL,
  org_level INTEGER DEFAULT 0,
  device_type VARCHAR(100),
  status VARCHAR(50) DEFAULT 'active',
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第三步：创建数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sync_type VARCHAR(50) DEFAULT 'manual',
  sync_status VARCHAR(50) DEFAULT 'running',
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  organizations_count INTEGER DEFAULT 0,
  channels_count INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第四步：创建API访问日志表
CREATE TABLE IF NOT EXISTS api_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) DEFAULT 'GET',
  status_code INTEGER DEFAULT 200,
  response_time_ms INTEGER,
  user_agent TEXT,
  ip_address INET,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第五步：创建视频流会话表
CREATE TABLE IF NOT EXISTS video_stream_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  stream_url VARCHAR(500) NOT NULL,
  protocol VARCHAR(50) DEFAULT 'HLS',
  user_id VARCHAR(255),
  session_token VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_accessed TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0
);

-- 第六步：创建视频事件记录表
CREATE TABLE IF NOT EXISTS video_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL,
  event_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity VARCHAR(50) DEFAULT 'info',
  description TEXT,
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE,
  processed_by VARCHAR(255),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第七步：创建视频监控点表
CREATE TABLE IF NOT EXISTS video_monitoring_points (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  point_name VARCHAR(255) NOT NULL,
  description TEXT,
  stream_url VARCHAR(500),
  backup_stream_url VARCHAR(500),
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  location_name VARCHAR(255),
  camera_type VARCHAR(100),
  resolution VARCHAR(50),
  viewing_angle INTEGER,
  night_vision BOOLEAN DEFAULT FALSE,
  ptz_control BOOLEAN DEFAULT FALSE,
  monitoring_area VARCHAR(255),
  installation_date DATE,
  maintenance_contact VARCHAR(255),
  point_status VARCHAR(50) DEFAULT 'active',
  device_ip INET,
  device_port INTEGER DEFAULT 80,
  channel_code VARCHAR(100),
  device_username VARCHAR(100),
  device_password_encrypted TEXT,
  device_status VARCHAR(50) DEFAULT 'offline',
  device_type VARCHAR(50) DEFAULT 'generic',
  last_heartbeat TIMESTAMP WITH TIME ZONE,
  device_info JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 第八步：创建索引
CREATE INDEX IF NOT EXISTS idx_organizations_parent_id ON organizations(parent_id);
CREATE INDEX IF NOT EXISTS idx_organizations_level ON organizations(level);
CREATE INDEX IF NOT EXISTS idx_video_channels_org_id ON video_channels(org_id);
CREATE INDEX IF NOT EXISTS idx_video_channels_status ON video_channels(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_type ON sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_status ON sync_logs(sync_status);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_endpoint ON api_access_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_created_at ON api_access_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_channel_id ON video_stream_sessions(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_stream_sessions_expires_at ON video_stream_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_video_events_channel_id ON video_events(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_events_event_time ON video_events(event_time);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_location ON video_monitoring_points(location_lat, location_lng);
CREATE INDEX IF NOT EXISTS idx_video_monitoring_points_status ON video_monitoring_points(point_status);

-- 第九步：创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 第十步：创建更新时间触发器
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE
    ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_channels_updated_at BEFORE UPDATE
    ON video_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_monitoring_points_updated_at BEFORE UPDATE
    ON video_monitoring_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 第十一步：启用行级安全性
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_monitoring_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_stream_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_events ENABLE ROW LEVEL SECURITY;

-- 第十二步：创建策略
CREATE POLICY "Allow all operations on organizations" ON organizations
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_channels" ON video_channels
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_monitoring_points" ON video_monitoring_points
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on sync_logs" ON sync_logs
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on api_access_logs" ON api_access_logs
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_stream_sessions" ON video_stream_sessions
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on video_events" ON video_events
    FOR ALL USING (true) WITH CHECK (true);
