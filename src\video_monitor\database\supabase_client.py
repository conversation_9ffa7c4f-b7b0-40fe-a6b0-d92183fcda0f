"""
Supabase客户端模块

提供Supabase数据库连接和基本操作功能
"""

import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
from postgrest.exceptions import APIError

logger = logging.getLogger(__name__)


class SupabaseClient:
    """Supabase客户端类"""
    
    def __init__(self, url: str, key: str):
        """
        初始化Supabase客户端
        
        Args:
            url: Supabase项目URL
            key: Supabase匿名密钥
        """
        self.url = url
        self.key = key
        self._client: Optional[Client] = None
        
    def connect(self) -> bool:
        """
        连接到Supabase
        
        Returns:
            连接是否成功
        """
        try:
            self._client = create_client(self.url, self.key)
            logger.info("✅ Supabase连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase连接失败: {e}")
            return False
    
    @property
    def client(self) -> Client:
        """获取Supabase客户端实例"""
        if self._client is None:
            if not self.connect():
                raise RuntimeError("Supabase客户端未连接")
        return self._client
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            连接是否正常
        """
        try:
            # 尝试查询一个简单的表来测试连接
            result = self.client.table('organizations').select('count').limit(1).execute()
            logger.info("✅ Supabase连接测试成功")
            return True
        except Exception as e:
            logger.warning(f"⚠️ Supabase连接测试失败: {e}")
            return False
    
    def insert_data(self, table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        插入数据
        
        Args:
            table: 表名
            data: 要插入的数据
            
        Returns:
            插入的数据或None
        """
        try:
            result = self.client.table(table).insert(data).execute()
            if result.data:
                logger.debug(f"✅ 数据插入成功到表 {table}")
                return result.data[0] if result.data else None
            return None
        except APIError as e:
            logger.error(f"❌ 插入数据到表 {table} 失败: {e}")
            return None
    
    def upsert_data(self, table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        插入或更新数据
        
        Args:
            table: 表名
            data: 要插入或更新的数据
            
        Returns:
            操作后的数据或None
        """
        try:
            result = self.client.table(table).upsert(data).execute()
            if result.data:
                logger.debug(f"✅ 数据upsert成功到表 {table}")
                return result.data[0] if result.data else None
            return None
        except APIError as e:
            logger.error(f"❌ Upsert数据到表 {table} 失败: {e}")
            return None
    
    def select_data(self, table: str, columns: str = "*", 
                   filters: Optional[Dict[str, Any]] = None,
                   limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        查询数据
        
        Args:
            table: 表名
            columns: 要查询的列
            filters: 过滤条件
            limit: 限制返回数量
            
        Returns:
            查询结果列表
        """
        try:
            query = self.client.table(table).select(columns)
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            if limit:
                query = query.limit(limit)
            
            result = query.execute()
            return result.data if result.data else []
        except APIError as e:
            logger.error(f"❌ 查询表 {table} 失败: {e}")
            return []
    
    def update_data(self, table: str, data: Dict[str, Any], 
                   filters: Dict[str, Any]) -> bool:
        """
        更新数据
        
        Args:
            table: 表名
            data: 要更新的数据
            filters: 更新条件
            
        Returns:
            更新是否成功
        """
        try:
            query = self.client.table(table).update(data)
            
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            logger.debug(f"✅ 表 {table} 数据更新成功")
            return True
        except APIError as e:
            logger.error(f"❌ 更新表 {table} 失败: {e}")
            return False
    
    def delete_data(self, table: str, filters: Dict[str, Any]) -> bool:
        """
        删除数据
        
        Args:
            table: 表名
            filters: 删除条件
            
        Returns:
            删除是否成功
        """
        try:
            query = self.client.table(table).delete()
            
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            logger.debug(f"✅ 表 {table} 数据删除成功")
            return True
        except APIError as e:
            logger.error(f"❌ 删除表 {table} 数据失败: {e}")
            return False
    
    def execute_sql(self, sql: str) -> Optional[List[Dict[str, Any]]]:
        """
        执行原生SQL查询
        
        Args:
            sql: SQL语句
            
        Returns:
            查询结果或None
        """
        try:
            result = self.client.rpc('execute_sql', {'sql': sql}).execute()
            return result.data if result.data else []
        except APIError as e:
            logger.error(f"❌ 执行SQL失败: {e}")
            return None


# 全局Supabase客户端实例
_global_supabase_client: Optional[SupabaseClient] = None


def get_supabase_client() -> SupabaseClient:
    """获取全局Supabase客户端实例"""
    global _global_supabase_client
    if _global_supabase_client is None:
        # 使用提供的Supabase配置
        url = "http://120.55.84.198:8000/"
        key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
        _global_supabase_client = SupabaseClient(url, key)
    return _global_supabase_client


def init_supabase_client(url: str, key: str) -> SupabaseClient:
    """
    初始化全局Supabase客户端实例
    
    Args:
        url: Supabase项目URL
        key: Supabase匿名密钥
        
    Returns:
        Supabase客户端实例
    """
    global _global_supabase_client
    _global_supabase_client = SupabaseClient(url, key)
    return _global_supabase_client
