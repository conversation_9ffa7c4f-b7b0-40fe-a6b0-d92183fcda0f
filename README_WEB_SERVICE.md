# 海事系统视频数据后端服务

基于FastAPI和Supabase的现代化海事视频监控系统后端服务，提供RESTful API接口用于查询视频流URI地址、管理组织机构和视频通道信息、控制数据同步等功能。

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Supabase数据库服务
- 网络连接

### 2. 安装依赖

```bash
# 安装Python依赖包
pip install fastapi uvicorn pydantic requests supabase apscheduler python-multipart
```

### 3. 配置检查

运行配置检查脚本，确保所有依赖和配置正确：

```bash
python tests/check_configuration.py
```

### 4. 启动服务

```bash
# 进入项目目录
cd src/video_monitor

# 启动Web服务器
python run_web_server.py --host 0.0.0.0 --port 8080

# 或者使用调试模式
python run_web_server.py --debug
```

### 5. 验证服务

```bash
# 健康检查
curl http://localhost:8080/health

# 查看API文档
# 浏览器访问: http://localhost:8080/docs
```

## 🧪 功能测试

### 自动化测试

运行完整的API功能测试：

```bash
python tests/test_api_endpoints.py
```

指定不同的服务地址：

```bash
python tests/test_api_endpoints.py --url http://your-server:8080
```

### 手动测试

#### 1. 数据同步测试

```bash
# 启动数据同步
curl -X POST "http://localhost:8080/api/v1/sync/start" \
  -H "Content-Type: application/json" \
  -d '{"sync_type": "manual"}'

# 查看同步状态
curl "http://localhost:8080/api/v1/sync/status"
```

#### 2. 组织机构查询测试

```bash
# 获取组织机构列表
curl "http://localhost:8080/api/v1/organizations?page=1&page_size=10"

# 获取单个组织机构（替换ORG001为实际ID）
curl "http://localhost:8080/api/v1/organizations/ORG001"
```

#### 3. 视频通道查询测试

```bash
# 获取视频通道列表
curl "http://localhost:8080/api/v1/channels?page=1&page_size=10"

# 获取单个视频通道（替换CH001为实际ID）
curl "http://localhost:8080/api/v1/channels/CH001"
```

#### 4. 视频流URI获取测试

```bash
# 获取视频流URI（替换CH001为实际通道ID）
curl "http://localhost:8080/api/v1/streams/uri/CH001?protocol=HLS&quality=high"
```

## 📚 API文档

详细的API文档请查看：

- **在线文档**: http://localhost:8080/docs (Swagger UI)
- **ReDoc文档**: http://localhost:8080/redoc
- **文档文件**: [docs/API_DOCUMENTATION.md](docs/API_DOCUMENTATION.md)

## 🏗️ 项目结构

```
video_monitor_python/
├── src/video_monitor/           # 主要源代码
│   ├── config/                  # 配置模块
│   │   └── settings.py         # 配置设置
│   ├── database/               # 数据库模块
│   │   ├── supabase_client.py  # Supabase客户端
│   │   └── setup_database.sql  # 数据库设置脚本
│   ├── sync/                   # 数据同步模块
│   │   └── sync_service.py     # 同步服务
│   ├── web/                    # Web API模块
│   │   ├── app.py              # FastAPI应用
│   │   ├── models.py           # API数据模型
│   │   ├── middleware.py       # 中间件
│   │   └── routes/             # API路由
│   │       ├── __init__.py
│   │       ├── video_streams.py
│   │       ├── organizations.py
│   │       ├── video_channels.py
│   │       ├── sync.py
│   │       └── logs.py
│   └── run_web_server.py       # Web服务器启动脚本
├── tests/                      # 测试脚本
│   ├── check_configuration.py  # 配置检查
│   └── test_api_endpoints.py   # API功能测试
├── docs/                       # 文档
│   └── API_DOCUMENTATION.md    # API文档
└── README_WEB_SERVICE.md       # Web服务说明
```

## 🔧 配置说明

### 环境变量

系统支持以下环境变量配置：

- `SUPABASE_URL`: Supabase服务URL（默认: http://*************:8000/）
- `SUPABASE_KEY`: Supabase服务密钥
- `LOG_LEVEL`: 日志级别（默认: INFO）
- `API_HOST`: API服务监听地址（默认: 0.0.0.0）
- `API_PORT`: API服务监听端口（默认: 8080）

### 配置文件

主要配置在 `src/video_monitor/config/settings.py` 中，包括：

- Supabase连接配置
- 同步服务配置
- 日志配置
- API服务配置

## 🔄 数据同步

系统提供自动和手动两种数据同步方式：

### 自动同步

- 系统启动后会自动启动定时同步任务
- 默认每小时执行一次完整同步
- 同步内容包括组织机构和视频通道信息

### 手动同步

通过API接口可以手动触发同步：

```bash
# 完整同步
curl -X POST "http://localhost:8080/api/v1/sync/start"

# 只同步组织机构
curl -X POST "http://localhost:8080/api/v1/sync/organizations"

# 只同步视频通道
curl -X POST "http://localhost:8080/api/v1/sync/channels"
```

## 📊 监控和日志

### 访问日志

系统会记录所有API访问日志，可通过以下接口查询：

```bash
# 获取访问日志
curl "http://localhost:8080/api/v1/logs/access?page=1&page_size=20"

# 获取访问统计
curl "http://localhost:8080/api/v1/logs/access/stats?days=7"

# 获取错误日志
curl "http://localhost:8080/api/v1/logs/errors?days=7"
```

### 同步日志

数据同步过程会记录详细日志：

```bash
# 获取同步日志
curl "http://localhost:8080/api/v1/sync/logs?page=1&page_size=20"
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Python版本是否 >= 3.8
   - 检查依赖包是否完整安装
   - 检查端口是否被占用

2. **数据库连接失败**
   - 检查Supabase服务是否正常运行
   - 检查网络连接
   - 验证连接配置是否正确

3. **同步失败**
   - 检查源数据服务是否可访问
   - 查看同步日志了解具体错误
   - 检查数据库权限设置

### 日志查看

```bash
# 查看服务日志
tail -f video_monitor_web.log

# 查看错误日志
grep "ERROR" video_monitor_web.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。
