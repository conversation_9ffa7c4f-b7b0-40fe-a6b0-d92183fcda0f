"""
会话保持模块

提供自动会话保活和重新登录功能
"""

import logging
import threading
import time
from typing import Optional, Callable
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class SessionKeeper:
    """会话保持器，负责自动保活和重新登录"""
    
    def __init__(self, login_callback: Callable[[], tuple[bool, Optional[str], Optional[str]]], 
                 keepalive_interval: int = 600):  # 10分钟保活一次
        """
        初始化会话保持器
        
        Args:
            login_callback: 登录回调函数，返回(成功标志, Token, 错误信息)
            keepalive_interval: 保活间隔时间（秒）
        """
        self.login_callback = login_callback
        self.keepalive_interval = keepalive_interval
        
        self._token: Optional[str] = None
        self._last_login_time: Optional[datetime] = None
        self._is_running = False
        self._keepalive_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        logger.info(f"会话保持器初始化完成，保活间隔: {keepalive_interval}秒")
    
    @property
    def token(self) -> Optional[str]:
        """获取当前有效的Token"""
        return self._token
    
    @property
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self._token is not None
    
    @property
    def last_login_time(self) -> Optional[datetime]:
        """获取最后登录时间"""
        return self._last_login_time
    
    def start(self) -> bool:
        """
        启动会话保持服务
        
        Returns:
            启动是否成功
        """
        if self._is_running:
            logger.warning("会话保持服务已在运行")
            return True
        
        logger.info("🚀 启动会话保持服务...")
        
        # 首次登录
        if not self._perform_login():
            logger.error("❌ 初始登录失败，无法启动会话保持服务")
            return False
        
        # 启动保活线程
        self._is_running = True
        self._stop_event.clear()
        self._keepalive_thread = threading.Thread(target=self._keepalive_loop, daemon=True)
        self._keepalive_thread.start()
        
        logger.info("✅ 会话保持服务启动成功")
        return True
    
    def stop(self) -> None:
        """停止会话保持服务"""
        if not self._is_running:
            return
        
        logger.info("🛑 停止会话保持服务...")
        
        self._is_running = False
        self._stop_event.set()
        
        if self._keepalive_thread and self._keepalive_thread.is_alive():
            self._keepalive_thread.join(timeout=5)
        
        logger.info("✅ 会话保持服务已停止")
    
    def force_relogin(self) -> bool:
        """
        强制重新登录
        
        Returns:
            重新登录是否成功
        """
        logger.info("🔄 强制重新登录...")
        return self._perform_login()
    
    def _perform_login(self) -> bool:
        """
        执行登录
        
        Returns:
            登录是否成功
        """
        try:
            success, token, error_message = self.login_callback()
            
            if success and token:
                self._token = token
                self._last_login_time = datetime.now()
                logger.info("✅ 登录成功，Token已更新")
                return True
            else:
                logger.error(f"❌ 登录失败: {error_message}")
                self._token = None
                self._last_login_time = None
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录过程中发生异常: {e}")
            self._token = None
            self._last_login_time = None
            return False
    
    def _keepalive_loop(self) -> None:
        """保活循环"""
        logger.info("🔄 会话保活循环开始")
        
        while self._is_running and not self._stop_event.is_set():
            try:
                # 等待保活间隔时间
                if self._stop_event.wait(timeout=self.keepalive_interval):
                    break  # 收到停止信号
                
                if not self._is_running:
                    break
                
                # 检查是否需要重新登录
                if self._should_relogin():
                    logger.info("🔄 会话即将过期，执行重新登录...")
                    if not self._perform_login():
                        logger.error("❌ 重新登录失败，将在下次循环重试")
                else:
                    logger.debug("✅ 会话状态正常")
                
            except Exception as e:
                logger.error(f"❌ 保活循环中发生异常: {e}")
                # 继续循环，不退出
        
        logger.info("🛑 会话保活循环结束")
    
    def _should_relogin(self) -> bool:
        """
        判断是否应该重新登录
        
        Returns:
            是否需要重新登录
        """
        if not self._token or not self._last_login_time:
            return True
        
        # 如果距离上次登录超过50分钟，则重新登录（Token通常1小时过期）
        time_since_login = datetime.now() - self._last_login_time
        return time_since_login > timedelta(minutes=50)
    
    def get_status(self) -> dict:
        """
        获取会话状态信息
        
        Returns:
            状态信息字典
        """
        return {
            "is_running": self._is_running,
            "is_logged_in": self.is_logged_in,
            "token": self._token[:20] + "..." if self._token else None,
            "last_login_time": self._last_login_time.isoformat() if self._last_login_time else None,
            "keepalive_interval": self.keepalive_interval,
            "time_since_login": (
                str(datetime.now() - self._last_login_time) 
                if self._last_login_time else None
            )
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()


def create_session_keeper(login_callback: Callable[[], tuple[bool, Optional[str], Optional[str]]], 
                         keepalive_interval: int = 600) -> SessionKeeper:
    """
    创建会话保持器的便捷函数
    
    Args:
        login_callback: 登录回调函数
        keepalive_interval: 保活间隔时间（秒）
        
    Returns:
        会话保持器实例
    """
    return SessionKeeper(login_callback, keepalive_interval)
