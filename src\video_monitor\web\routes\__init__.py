"""
API路由模块

包含所有API路由定义
"""

from fastapi import APIRouter

from .organizations import router as organizations_router
from .video_channels import router as video_channels_router
from .video_streams import router as video_streams_router
from .sync import router as sync_router
from .logs import router as logs_router

# 创建主路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(
    organizations_router,
    prefix="/organizations",
    tags=["组织机构"]
)

api_router.include_router(
    video_channels_router,
    prefix="/channels",
    tags=["视频通道"]
)

api_router.include_router(
    video_streams_router,
    prefix="/streams",
    tags=["视频流"]
)

api_router.include_router(
    sync_router,
    prefix="/sync",
    tags=["数据同步"]
)

api_router.include_router(
    logs_router,
    prefix="/logs",
    tags=["日志管理"]
)

__all__ = ["api_router"]
