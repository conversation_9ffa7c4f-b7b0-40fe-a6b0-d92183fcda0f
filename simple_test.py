#!/usr/bin/env python3
"""
简单的Supabase连接测试
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from supabase import create_client
    
    # Supabase配置
    url = "http://120.55.84.198:8000/"
    key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
    
    print("🔍 尝试连接到Supabase...")
    print(f"URL: {url}")
    
    # 创建客户端
    supabase = create_client(url, key)
    print("✅ Supabase客户端创建成功")
    
    # 测试简单查询
    print("🔍 测试数据库连接...")
    try:
        # 尝试查询一个可能存在的表
        result = supabase.table('organizations').select('*').limit(1).execute()
        print(f"✅ 查询成功，返回数据: {result.data}")
    except Exception as e:
        print(f"ℹ️ 查询organizations表失败（可能表不存在）: {e}")
        
        # 尝试查询另一个表
        try:
            result = supabase.table('video_channels').select('*').limit(1).execute()
            print(f"✅ 查询video_channels成功，返回数据: {result.data}")
        except Exception as e2:
            print(f"ℹ️ 查询video_channels表失败（可能表不存在）: {e2}")
    
    print("🎉 Supabase连接测试完成")
    
except Exception as e:
    print(f"❌ 连接失败: {e}")
    import traceback
    traceback.print_exc()
