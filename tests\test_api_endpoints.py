#!/usr/bin/env python3
"""
API接口功能测试脚本

测试所有主要API接口的功能
"""

import sys
import json
import time
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        """
        初始化API测试器
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        logger.info("🔍 测试健康检查接口...")
        
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ 健康检查通过")
                return True
            else:
                logger.error(f"❌ 健康检查失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 健康检查异常: {e}")
            return False
    
    def test_sync_endpoints(self) -> bool:
        """测试数据同步接口"""
        logger.info("🔍 测试数据同步接口...")
        
        try:
            # 1. 获取同步状态
            response = self.session.get(f"{self.api_base}/sync/status")
            if response.status_code != 200:
                logger.error(f"❌ 获取同步状态失败: {response.status_code}")
                return False
            
            status_data = response.json()
            logger.info(f"📊 同步状态: {status_data.get('data', {}).get('is_syncing', 'unknown')}")
            
            # 2. 启动数据同步
            sync_request = {
                "sync_type": "manual",
                "force": False
            }
            
            response = self.session.post(
                f"{self.api_base}/sync/start",
                json=sync_request
            )
            
            if response.status_code == 200:
                logger.info("✅ 数据同步启动成功")
            elif response.status_code == 409:
                logger.info("⚠️ 同步任务已在进行中")
            else:
                logger.error(f"❌ 启动数据同步失败: {response.status_code}")
                return False
            
            # 3. 等待一段时间后再次检查状态
            time.sleep(2)
            response = self.session.get(f"{self.api_base}/sync/status")
            if response.status_code == 200:
                logger.info("✅ 同步状态查询成功")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 测试同步接口异常: {e}")
            return False
    
    def test_organizations_endpoints(self) -> bool:
        """测试组织机构接口"""
        logger.info("🔍 测试组织机构接口...")
        
        try:
            # 1. 获取组织机构列表
            response = self.session.get(
                f"{self.api_base}/organizations",
                params={"page": 1, "page_size": 10}
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 获取组织机构列表失败: {response.status_code}")
                return False
            
            orgs_data = response.json()
            organizations = orgs_data.get("data", [])
            total = orgs_data.get("total", 0)
            
            logger.info(f"📋 组织机构总数: {total}")
            
            if organizations:
                # 2. 测试获取单个组织机构
                first_org = organizations[0]
                org_id = first_org.get("org_id")
                
                if org_id:
                    response = self.session.get(f"{self.api_base}/organizations/{org_id}")
                    if response.status_code == 200:
                        logger.info(f"✅ 获取组织机构 {org_id} 成功")
                    else:
                        logger.error(f"❌ 获取组织机构 {org_id} 失败: {response.status_code}")
                        return False
                    
                    # 3. 测试获取子级组织
                    response = self.session.get(f"{self.api_base}/organizations/{org_id}/children")
                    if response.status_code == 200:
                        logger.info(f"✅ 获取组织机构 {org_id} 子级成功")
                    else:
                        logger.error(f"❌ 获取组织机构 {org_id} 子级失败: {response.status_code}")
                        return False
            
            logger.info("✅ 组织机构接口测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试组织机构接口异常: {e}")
            return False
    
    def test_channels_endpoints(self) -> bool:
        """测试视频通道接口"""
        logger.info("🔍 测试视频通道接口...")
        
        try:
            # 1. 获取视频通道列表
            response = self.session.get(
                f"{self.api_base}/channels",
                params={"page": 1, "page_size": 10}
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 获取视频通道列表失败: {response.status_code}")
                return False
            
            channels_data = response.json()
            channels = channels_data.get("data", [])
            total = channels_data.get("total", 0)
            
            logger.info(f"📺 视频通道总数: {total}")
            
            if channels:
                # 2. 测试获取单个视频通道
                first_channel = channels[0]
                channel_id = first_channel.get("channel_id")
                
                if channel_id:
                    response = self.session.get(f"{self.api_base}/channels/{channel_id}")
                    if response.status_code == 200:
                        logger.info(f"✅ 获取视频通道 {channel_id} 成功")
                    else:
                        logger.error(f"❌ 获取视频通道 {channel_id} 失败: {response.status_code}")
                        return False
            
            logger.info("✅ 视频通道接口测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试视频通道接口异常: {e}")
            return False
    
    def test_video_streams_endpoints(self) -> bool:
        """测试视频流接口"""
        logger.info("🔍 测试视频流接口...")
        
        try:
            # 首先获取一个可用的通道ID
            response = self.session.get(
                f"{self.api_base}/channels",
                params={"page": 1, "page_size": 1}
            )
            
            if response.status_code != 200:
                logger.warning("⚠️ 无法获取视频通道列表，跳过视频流测试")
                return True
            
            channels_data = response.json()
            channels = channels_data.get("data", [])
            
            if not channels:
                logger.warning("⚠️ 没有可用的视频通道，跳过视频流测试")
                return True
            
            channel_id = channels[0].get("channel_id")
            
            # 测试获取视频流URI
            response = self.session.get(
                f"{self.api_base}/streams/uri/{channel_id}",
                params={
                    "protocol": "HLS",
                    "quality": "high"
                }
            )
            
            if response.status_code == 200:
                stream_data = response.json()
                logger.info(f"✅ 获取视频流URI成功: {channel_id}")
                
                # 检查响应数据结构
                data = stream_data.get("data", {})
                if "stream_url" in data and "session_token" in data:
                    logger.info("✅ 视频流响应数据结构正确")
                else:
                    logger.warning("⚠️ 视频流响应数据结构不完整")
                
            elif response.status_code == 404:
                logger.warning(f"⚠️ 视频通道 {channel_id} 不存在或无流地址")
            else:
                logger.error(f"❌ 获取视频流URI失败: {response.status_code}")
                return False
            
            logger.info("✅ 视频流接口测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试视频流接口异常: {e}")
            return False
    
    def test_logs_endpoints(self) -> bool:
        """测试日志接口"""
        logger.info("🔍 测试日志接口...")
        
        try:
            # 1. 获取API访问日志
            response = self.session.get(
                f"{self.api_base}/logs/access",
                params={"page": 1, "page_size": 5}
            )
            
            if response.status_code == 200:
                logger.info("✅ 获取API访问日志成功")
            else:
                logger.error(f"❌ 获取API访问日志失败: {response.status_code}")
                return False
            
            # 2. 获取访问统计
            response = self.session.get(
                f"{self.api_base}/logs/access/stats",
                params={"days": 7}
            )
            
            if response.status_code == 200:
                logger.info("✅ 获取API访问统计成功")
            else:
                logger.error(f"❌ 获取API访问统计失败: {response.status_code}")
                return False
            
            logger.info("✅ 日志接口测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试日志接口异常: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        logger.info("🚀 开始API接口功能测试")
        logger.info(f"🔗 测试目标: {self.base_url}")
        
        tests = [
            ("健康检查", self.test_health_check),
            ("数据同步", self.test_sync_endpoints),
            ("组织机构", self.test_organizations_endpoints),
            ("视频通道", self.test_channels_endpoints),
            ("视频流", self.test_video_streams_endpoints),
            ("日志管理", self.test_logs_endpoints),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 测试模块: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
        
        logger.info(f"\n{'='*50}")
        logger.info(f"📊 测试结果汇总")
        logger.info(f"{'='*50}")
        logger.info(f"✅ 通过: {passed}/{total}")
        logger.info(f"❌ 失败: {total - passed}/{total}")
        logger.info(f"📈 成功率: {(passed/total)*100:.1f}%")
        
        return passed == total


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="API接口功能测试")
    parser.add_argument(
        '--url',
        default='http://localhost:8080',
        help='API服务地址 (默认: http://localhost:8080)'
    )
    
    args = parser.parse_args()
    
    # 创建测试器并运行测试
    tester = APITester(args.url)
    success = tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 部分测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
