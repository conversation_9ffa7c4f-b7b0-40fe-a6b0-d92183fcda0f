2025-07-03 02:55:19,078 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:55:19,078 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:55:19,078 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:19,078 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:19,079 - __main__ - ERROR - 初始化通道管理器失败: Retry.__init__() got an unexpected keyword argument 'method_whitelist'
2025-07-03 02:55:19,079 - __main__ - ERROR - 程序执行异常: Retry.__init__() got an unexpected keyword argument 'method_whitelist'
2025-07-03 02:55:48,063 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:55:48,063 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:55:48,063 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:48,064 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:48,064 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 02:55:48,065 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 02:55:48,065 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 02:55:48,065 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 02:55:48,065 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 02:55:48,066 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 02:55:48,066 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 02:55:48,106 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 02:55:48,124 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 02:55:48,124 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 02:55:48,378 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 02:55:48,630 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 02:55:48,630 - video_monitor.config.settings - INFO - Updated configuration token.token = 12563364726704256_b7JmRXztm0hFgoLL4ZpBQhwrGco1HhOf2OMfvCc7fU
2025-07-03 02:55:48,631 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 02:55:48,631 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 02:55:48,631 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 02:55:48,632 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 02:55:48,632 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 02:55:48,632 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 02:55:55,879 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 02:55:59,205 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 02:56:01,706 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 02:56:02,264 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 02:56:02,598 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 02:56:02,598 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 02:56:02,598 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 02:56:02,598 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 02:56:02,600 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 02:56:02,600 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 02:56:03,331 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751482721_32521729e5f39e9c19cfa85f50472e514f639927
2025-07-03 02:56:03,332 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751482721_32521729e5f39e9c19cfa85f50472e514f639927
2025-07-03 02:56:03,332 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 02:56:03,332 - __main__ - INFO - 🚪 开始登出...
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 02:56:03,611 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 02:56:03,611 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 02:56:03,611 - __main__ - INFO - ✅ 登出完成
2025-07-03 02:56:25,485 - video_monitor.main - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:56:25,485 - video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:56:25,486 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:56:25,486 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:56:25,486 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 02:56:25,487 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 02:56:25,487 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 02:56:25,487 - video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 02:56:25,487 - video_monitor.main - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 02:56:25,488 - video_monitor.main - INFO - 发现已保存的token，尝试验证...
2025-07-03 02:56:25,488 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 02:56:25,527 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 02:56:25,531 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 02:56:25,532 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 02:56:25,732 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 02:56:25,732 - video_monitor.main - INFO - ✅ 使用已保存的token登录成功
2025-07-03 02:56:25,732 - video_monitor.main - INFO - 📡 开始获取所有通道信息...
2025-07-03 02:56:25,733 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 02:56:25,939 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 02:56:25,939 - video_monitor.main - INFO - ✅ 成功获取 0 个通道
2025-07-03 02:56:25,939 - video_monitor.main - WARNING - 没有找到任何通道
2025-07-03 02:56:25,939 - video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 02:56:25,939 - video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:00:21,224 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:00:21,224 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:00:21,225 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:21,225 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:21,225 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:00:21,226 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:00:21,226 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:00:21,227 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:00:21,227 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:00:21,227 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:00:21,227 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:00:21,267 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:00:21,271 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:00:21,272 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:00:21,542 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:00:21,543 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:00:21,543 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:00:21,543 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:00:21,789 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:00:21,790 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:00:21,790 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:00:21,790 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:00:21,790 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:00:47,219 - video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:00:47,219 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:47,220 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:47,220 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:00:47,221 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:00:47,221 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:00:47,221 - video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:00:47,222 - video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 03:00:47,222 - video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:01:50,354 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:01:50,355 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:01:50,355 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:01:50,356 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:01:50,356 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:01:50,357 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:01:50,357 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:01:50,357 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:01:50,357 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:01:50,358 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:01:50,358 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:01:50,398 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:01:50,403 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:01:50,403 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:01:50,685 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:01:50,686 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:01:50,686 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:01:50,686 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:01:50,947 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:01:50,948 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:01:50,948 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:01:50,948 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:01:50,948 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:02:16,826 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:02:16,827 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:02:16,827 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:16,827 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:16,828 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:02:16,828 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:02:16,828 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:02:16,829 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:02:16,829 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:02:16,829 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:02:16,829 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:02:16,869 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:02:16,873 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:02:16,873 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:02:17,133 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:02:17,133 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:02:17,133 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:02:17,134 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:02:17,394 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:02:17,394 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:02:17,394 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:02:17,395 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:02:17,395 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:02:35,933 - src.video_monitor.main - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:02:35,933 - src.video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:02:35,934 - src.video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:35,934 - src.video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:35,934 - src.video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:02:35,935 - src.video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.main - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:02:35,936 - src.video_monitor.main - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:02:35,936 - src.video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:02:35,976 - src.video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:02:35,980 - src.video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:02:35,980 - src.video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:02:36,216 - src.video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:02:36,216 - src.video_monitor.main - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:02:36,217 - src.video_monitor.main - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:02:36,217 - src.video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:02:36,506 - src.video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:02:36,506 - src.video_monitor.main - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:02:36,507 - src.video_monitor.main - WARNING - 没有找到任何通道
2025-07-03 03:02:36,507 - src.video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 03:02:36,507 - src.video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:08:53,456 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:08:53,456 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:08:53,456 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:08:53,457 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:08:53,457 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:08:53,458 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:08:53,458 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:08:53,458 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:08:53,458 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:08:53,458 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:08:53,459 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:08:53,498 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:08:53,516 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:08:53,516 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:08:53,756 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:08:53,757 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:08:53,757 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:08:53,757 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:08:54,009 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:08:54,010 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:08:54,010 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:08:54,010 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:08:54,011 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:23,735 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:23,735 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:23,735 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:23,736 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:23,736 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:23,737 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:09:23,737 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:23,737 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:23,737 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:23,738 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:23,738 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:23,779 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:23,784 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:23,785 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:24,022 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:24,022 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:24,023 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:24,023 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:24,282 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:24,282 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:24,282 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:24,283 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:24,283 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:28,260 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:28,260 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:28,261 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:28,261 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:28,262 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:28,262 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:09:28,262 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:28,263 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:28,263 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:28,263 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:28,263 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:28,304 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:28,308 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:28,309 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:28,556 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:28,557 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:28,557 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:28,557 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:28,814 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:28,814 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:28,815 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:28,815 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:28,815 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:46,116 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:46,117 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:46,117 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:46,117 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:46,118 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:46,118 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:09:46,118 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:46,119 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:46,119 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:46,119 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:46,119 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:46,159 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:46,163 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:46,163 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:46,420 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:46,420 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:46,421 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:46,421 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:46,669 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:46,670 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:46,670 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:46,670 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:46,670 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:48,930 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:48,931 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:48,931 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:48,932 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:48,932 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:48,933 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:09:48,933 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:48,933 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:48,933 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:48,933 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:48,934 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:48,973 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:48,978 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:48,978 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:49,203 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:49,204 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:49,204 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:49,204 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:49,465 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:49,466 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:49,466 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:49,466 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:49,466 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:12:18,157 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:12:18,157 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:12:18,158 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:12:18,158 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:12:18,158 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:12:18,159 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:12:18,159 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:12:18,159 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:12:18,159 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:12:18,160 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:12:18,160 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:12:18,200 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:12:18,205 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:12:18,205 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:12:18,461 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:12:18,461 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:12:18,461 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:12:18,461 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:12:18,699 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:12:18,699 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:12:18,700 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:12:18,700 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:12:18,700 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:15:06,086 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:15:06,086 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:15:06,086 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:15:06,087 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:15:06,087 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:15:06,088 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:15:06,088 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:15:06,088 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:15:06,088 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:15:06,089 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:15:06,089 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:15:06,128 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:15:06,132 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:15:06,133 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:15:06,346 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:15:06,347 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:15:06,347 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:15:06,347 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:15:06,594 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:15:06,594 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:15:06,594 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:15:06,594 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:15:06,595 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:22:15,251 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:22:15,251 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:22:15,252 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 03:22:15,252 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:22:15,252 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:22:15,252 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:22:15,253 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:22:15,253 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:22:15,253 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:22:15,253 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 03:22:15,253 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:22:15,293 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:22:15,297 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:22:15,297 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:22:15,565 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 03:22:15,848 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 03:22:15,849 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:22:15,849 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 03:22:15,849 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 03:22:15,850 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 03:22:15,850 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:22:15,850 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:22:22,971 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 03:22:26,148 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 03:22:28,682 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 03:22:29,285 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 03:22:29,619 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 03:22:29,619 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 03:22:29,620 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 03:22:29,620 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 03:22:29,620 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 03:22:29,620 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 03:22:29,620 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 03:22:29,620 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 03:22:29,621 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 03:22:29,621 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 03:22:30,274 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484308_722c1ad6378a1e370e202a8c0d4beb9ddbcd4c8a
2025-07-03 03:22:30,274 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484308_722c1ad6378a1e370e202a8c0d4beb9ddbcd4c8a
2025-07-03 03:22:30,275 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 03:22:30,275 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:22:30,275 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 03:22:30,275 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 03:22:30,275 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 03:22:30,557 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 03:22:30,557 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 03:22:30,558 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:23:19,970 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:23:19,971 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:23:19,971 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 03:23:19,972 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:23:19,972 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:23:19,972 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:23:19,973 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:23:19,973 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:23:19,973 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:23:19,973 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 03:23:19,973 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:23:20,013 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:23:20,017 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:23:20,018 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:23:20,229 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 03:23:20,444 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 03:23:20,444 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:23:20,445 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 03:23:20,445 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 03:23:20,445 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 03:23:20,446 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:23:20,446 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:23:27,815 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 03:23:31,364 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 03:23:33,897 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 03:23:34,475 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 03:23:34,827 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 03:23:34,827 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 03:23:34,827 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 03:23:34,828 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 03:23:34,828 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 03:23:34,828 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 03:23:34,828 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 03:23:34,829 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 03:23:34,829 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 03:23:34,829 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 03:23:35,446 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484373_52026e11fd9a3065bdb4efebddcf5fbaabd869c1
2025-07-03 03:23:35,447 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484373_52026e11fd9a3065bdb4efebddcf5fbaabd869c1
2025-07-03 03:23:35,447 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 03:23:35,447 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:23:35,448 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 03:23:35,448 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 03:23:35,448 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 03:23:35,655 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 03:23:35,656 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 03:23:35,656 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:25:59,131 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:25:59,131 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:25:59,132 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 03:25:59,132 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:25:59,132 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:25:59,132 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:25:59,133 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:25:59,133 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:25:59,133 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:25:59,133 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 03:25:59,133 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:25:59,174 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:25:59,178 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:25:59,178 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:25:59,446 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 03:25:59,678 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 03:25:59,679 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:25:59,679 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 03:25:59,679 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 03:25:59,680 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 03:25:59,680 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:25:59,680 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:26:06,832 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 03:26:10,229 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 03:26:12,713 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 03:26:13,258 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 03:26:13,565 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 03:26:13,565 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 03:26:13,566 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 03:26:13,566 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 03:26:13,566 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 03:26:13,566 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 03:26:13,566 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 03:26:13,567 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 03:26:13,567 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 03:26:13,567 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 03:26:14,222 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484532_6db5f33f3c6b78b61db4d4cc0c01c4c532fce379
2025-07-03 03:26:14,222 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484532_6db5f33f3c6b78b61db4d4cc0c01c4c532fce379
2025-07-03 03:26:14,223 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 03:26:14,223 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:26:14,223 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 03:26:14,223 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 03:26:14,223 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 03:26:14,505 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 03:26:14,505 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 03:26:14,506 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:32:08,333 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:32:08,333 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:32:08,333 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 03:32:08,334 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:32:08,334 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:32:08,334 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 03:32:08,334 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:32:08,334 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:32:08,335 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:32:08,335 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 03:32:08,335 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:32:08,375 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:32:08,379 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:32:08,379 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:32:08,622 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 03:32:08,861 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 03:32:08,861 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:32:08,862 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 03:32:08,862 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 03:32:08,862 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 03:32:08,863 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:32:08,863 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:32:15,947 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 03:32:19,405 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 03:32:21,828 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 03:32:22,537 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 03:32:22,869 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 03:32:22,869 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 03:32:22,869 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 03:32:22,869 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 03:32:22,869 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 03:32:22,870 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 03:32:22,870 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 03:32:22,870 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 03:32:22,870 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 03:32:22,870 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 03:32:23,482 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484901_a944dcf610fba5ef8ae5193c9f41837fc7a944d2
2025-07-03 03:32:23,483 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751484901_a944dcf610fba5ef8ae5193c9f41837fc7a944d2
2025-07-03 03:32:23,483 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 03:32:23,483 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:32:23,483 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 03:32:23,484 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 03:32:23,484 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 03:32:23,696 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 03:32:23,696 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 03:32:23,697 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:29:37,539 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:29:37,539 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:29:37,539 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:29:37,539 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:29:37,540 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:29:37,540 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:29:37,540 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:29:37,540 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:29:37,540 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:29:37,540 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:29:37,541 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:29:37,580 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:29:37,585 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:29:37,585 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:29:45,900 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:29:45,901 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:29:45,901 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:29:45,901 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:29:45,901 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:30:26,089 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:30:26,089 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:30:26,090 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:30:26,090 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:30:26,090 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:30:26,090 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:30:26,091 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:30:26,091 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:30:26,091 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:30:26,091 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:30:26,091 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:30:26,131 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:30:26,135 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:30:26,136 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:30:34,462 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:30:34,462 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:30:34,463 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:30:34,463 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:30:34,463 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:32:56,394 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:32:56,394 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:32:56,394 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:32:56,395 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:32:56,395 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:32:56,395 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:32:56,395 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:32:56,396 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:32:56,396 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:32:56,396 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:32:56,396 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:32:56,436 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:32:56,440 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:32:56,440 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:33:04,730 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:33:04,730 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:33:04,730 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:33:04,731 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:33:04,731 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:33:52,395 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:33:52,395 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:33:52,395 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:33:52,396 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:33:52,396 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:33:52,396 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:33:52,396 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:33:52,396 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:33:52,397 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:33:52,397 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:33:52,397 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:33:52,437 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:33:52,441 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:33:52,441 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:34:00,726 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:34:00,726 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-03 21:34:00,726 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:34:00,726 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:34:00,726 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:35:24,096 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:35:24,097 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:35:24,097 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:35:24,098 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:35:24,098 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:35:24,098 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:35:24,098 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:35:24,099 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:35:24,099 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:35:24,099 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:35:24,099 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:35:24,138 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:35:24,142 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:35:24,143 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:35:48,174 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:35:48,175 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:35:48,175 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:35:48,175 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:35:48,175 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:35:48,176 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:35:48,176 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:35:48,176 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:35:48,176 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:35:48,176 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:35:48,176 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:35:48,216 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:35:48,220 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:35:48,220 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:35:50,261 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001595B41E120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:35:50,261 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001595B41E120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:35:50,262 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:35:50,262 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:35:50,262 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:37:45,678 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:37:45,678 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:37:45,678 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:37:45,679 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:37:45,679 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:37:45,679 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:37:45,679 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:37:45,679 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:37:45,679 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:37:45,680 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:37:45,680 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:37:45,732 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:37:45,737 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:37:45,737 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:37:47,776 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D6091FA540>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:37:47,776 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D6091FA540>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:37:47,777 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:37:47,777 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:37:47,777 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:38:46,755 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:38:46,756 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:38:46,756 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:38:46,757 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:38:46,757 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:38:46,757 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:38:46,758 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:38:46,758 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:38:46,758 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:38:46,758 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:38:46,758 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:38:46,798 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:38:46,802 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:38:46,802 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:38:48,841 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001C6217A63F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:38:48,842 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001C6217A63F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:38:48,843 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:38:48,843 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:38:48,843 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:39:13,797 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:39:13,798 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:39:13,798 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:39:13,798 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:39:13,799 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:39:13,799 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:39:13,800 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:39:13,800 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:39:13,800 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:39:13,800 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:39:13,800 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:39:13,840 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:39:13,844 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:39:13,844 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:39:15,892 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023BB1E92360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:39:15,893 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023BB1E92360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:39:15,893 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:39:15,894 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:39:15,894 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:40:52,410 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:40:52,410 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:40:52,411 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:40:52,411 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:40:52,411 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:40:52,412 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:40:52,412 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:40:52,412 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:40:52,412 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:40:52,412 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:40:52,413 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:40:52,453 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:40:52,457 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:40:52,457 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:40:54,518 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000192D3ADA2A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:40:54,519 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000192D3ADA2A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:40:54,520 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:40:54,520 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:40:54,520 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:41:13,599 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:41:13,599 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:41:13,599 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:41:13,600 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:41:13,600 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:41:13,600 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:41:13,601 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:41:13,601 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:41:13,601 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:41:13,601 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:41:13,601 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:41:13,641 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:41:13,645 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:41:13,645 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:41:15,690 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000024B1286E120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:41:15,691 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000024B1286E120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:41:15,692 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:41:15,692 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:41:15,692 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:42:17,851 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:42:17,851 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:42:17,851 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:42:17,852 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:42:17,852 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:42:17,852 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:42:17,853 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:42:17,853 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:42:17,853 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:42:17,853 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:42:17,853 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:42:17,892 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:42:17,896 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:42:17,896 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:42:19,933 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000216CA95A540>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:42:19,934 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000216CA95A540>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:42:19,935 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:42:19,935 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:42:19,936 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:43:37,633 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:43:37,633 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:43:37,634 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:43:37,634 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:43:37,635 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:43:37,635 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:43:37,635 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:43:37,636 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:43:37,636 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:43:37,636 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:43:37,636 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:43:37,679 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:43:37,682 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:43:37,683 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:43:39,730 - video_monitor.api.client - ERROR - 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022745FCA480>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:43:39,731 - __main__ - ERROR - 登录失败: 第一步登录失败: 连接错误: HTTPSConnectionPool(host='**************', port=7282): Max retries exceeded with url: /videoService/accounts/authorize (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022745FCA480>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-07-03 21:43:39,732 - __main__ - ERROR - 登录失败，程序退出
2025-07-03 21:43:39,732 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:43:39,732 - __main__ - INFO - ✅ 登出完成
2025-07-03 21:44:52,417 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 21:44:52,417 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 21:44:52,418 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 21:44:52,418 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 21:44:52,418 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 21:44:52,418 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 21:44:52,419 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 21:44:52,419 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 21:44:52,419 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 21:44:52,419 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 21:44:52,419 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 21:44:52,458 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 21:44:52,461 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 21:44:52,462 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 21:44:52,778 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 21:44:53,051 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 21:44:53,052 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 21:44:53,052 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 21:44:53,052 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 21:44:53,053 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 21:44:53,053 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 21:44:53,053 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 21:45:01,626 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 21:45:05,811 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 21:45:08,685 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 21:45:09,402 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 21:45:09,757 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 21:45:09,759 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 21:45:09,760 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 21:45:09,761 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 21:45:09,762 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 21:45:09,764 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 21:45:09,765 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 21:45:09,766 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 21:45:09,766 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 21:45:09,767 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 21:45:10,473 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751550469_df94b8486e7661d8ab6cdc4f4697121ef2aa418a
2025-07-03 21:45:10,475 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://**************:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751550469_df94b8486e7661d8ab6cdc4f4697121ef2aa418a
2025-07-03 21:45:10,477 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 21:45:10,478 - __main__ - INFO - 🚪 开始登出...
2025-07-03 21:45:10,480 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 21:45:10,481 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 21:45:10,483 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 21:45:10,748 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 21:45:10,749 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 21:45:10,751 - __main__ - INFO - ✅ 登出完成
2025-07-03 22:14:25,873 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 22:14:25,874 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 22:14:25,874 - video_monitor.config.settings - INFO - 配置管理器初始化完成，使用硬编码配置
2025-07-03 22:14:25,874 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 22:14:25,875 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 22:14:25,875 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: **************:7282, 用户: haishi
2025-07-03 22:14:25,875 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 22:14:25,875 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 22:14:25,875 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 22:14:25,876 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 22:14:25,876 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 22:14:25,916 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 22:14:25,920 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 22:14:25,920 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 22:14:26,156 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 22:14:26,416 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 22:14:26,416 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 22:14:26,417 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 22:14:26,417 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 22:14:26,418 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 22:14:26,418 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 22:14:26,418 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 22:14:33,430 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
