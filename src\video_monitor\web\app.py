"""
FastAPI应用程序

海事系统视频数据后端服务的主要Web应用
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ..database.supabase_client import get_supabase_client
from ..business.monitor_service import MonitorService
from ..auth.session_keeper import create_session_keeper
from ..services.sync_service import SyncService
from ..services.scheduler_service import SchedulerService
from .routes import api_router
from .middleware import LoggingMiddleware, ApiAccessLogMiddleware
from .models import ErrorResponse

logger = logging.getLogger(__name__)

# 全局服务实例
app_services: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 启动海事系统视频数据后端服务...")
    
    try:
        # 初始化数据库连接
        supabase_client = get_supabase_client()
        if not supabase_client.connect():
            raise Exception("Supabase连接失败")
        app_services["supabase_client"] = supabase_client
        logger.info("✅ Supabase连接成功")
        
        # 初始化监控服务
        monitor_service = MonitorService()
        app_services["monitor_service"] = monitor_service
        logger.info("✅ 监控服务初始化完成")
        
        # 初始化同步服务
        sync_service = SyncService(supabase_client, monitor_service)
        app_services["sync_service"] = sync_service
        logger.info("✅ 同步服务初始化完成")
        
        # 初始化调度服务
        scheduler_service = SchedulerService()
        if scheduler_service.start():
            # 添加每小时同步任务
            scheduler_service.add_hourly_sync_job(
                lambda: sync_service.sync_all_data("scheduled")
            )
            app_services["scheduler_service"] = scheduler_service
            logger.info("✅ 调度服务启动成功")
        else:
            logger.warning("⚠️ 调度服务启动失败")
        
        # 初始化会话保持器
        def login_callback():
            try:
                if monitor_service.login():
                    return True, monitor_service.token, None
                else:
                    return False, None, "登录失败"
            except Exception as e:
                return False, None, str(e)
        
        session_keeper = create_session_keeper(login_callback)
        if session_keeper.start():
            app_services["session_keeper"] = session_keeper
            logger.info("✅ 会话保持器启动成功")
        else:
            logger.warning("⚠️ 会话保持器启动失败")
        
        logger.info("🎉 所有服务启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    
    finally:
        # 清理资源
        logger.info("🛑 正在关闭服务...")
        
        if "session_keeper" in app_services:
            app_services["session_keeper"].stop()
            logger.info("✅ 会话保持器已停止")
        
        if "scheduler_service" in app_services:
            app_services["scheduler_service"].stop()
            logger.info("✅ 调度服务已停止")
        
        logger.info("✅ 所有服务已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    
    Returns:
        配置好的FastAPI应用实例
    """
    
    # 创建FastAPI应用
    app = FastAPI(
        title="海事系统视频数据后端服务",
        description="提供视频通道查询、数据同步等功能的RESTful API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加可信主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境应该限制具体主机
    )
    
    # 添加自定义中间件
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(ApiAccessLogMiddleware)
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 全局异常处理器
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                message=exc.detail,
                error_code=f"HTTP_{exc.status_code}"
            ).dict()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理器"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                message="内部服务器错误",
                error_code="INTERNAL_SERVER_ERROR",
                error_details={"exception": str(exc)}
            ).dict()
        )
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径，返回服务信息"""
        return {
            "service": "海事系统视频数据后端服务",
            "version": "1.0.0",
            "status": "running",
            "docs": "/docs",
            "api": "/api/v1"
        }
    
    # 健康检查
    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        try:
            status = {
                "status": "healthy",
                "services": {}
            }
            
            # 检查数据库连接
            if "supabase_client" in app_services:
                db_status = "connected" if app_services["supabase_client"].test_connection() else "disconnected"
                status["services"]["database"] = db_status
            
            # 检查其他服务状态
            for service_name in ["monitor_service", "sync_service", "scheduler_service", "session_keeper"]:
                if service_name in app_services:
                    status["services"][service_name] = "running"
                else:
                    status["services"][service_name] = "stopped"
            
            return status
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return JSONResponse(
                status_code=503,
                content={"status": "unhealthy", "error": str(e)}
            )
    
    return app


def get_app_service(service_name: str):
    """
    获取应用服务实例
    
    Args:
        service_name: 服务名称
        
    Returns:
        服务实例
        
    Raises:
        HTTPException: 服务不可用时抛出异常
    """
    if service_name not in app_services:
        raise HTTPException(
            status_code=503,
            detail=f"服务 {service_name} 不可用"
        )
    return app_services[service_name]


def run_app(host: str = "0.0.0.0", port: int = 8080, debug: bool = False):
    """
    运行FastAPI应用
    
    Args:
        host: 监听主机
        port: 监听端口
        debug: 是否开启调试模式
    """
    app = create_app()
    
    # 配置日志
    log_level = "debug" if debug else "info"
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        log_level=log_level,
        access_log=True,
        reload=debug
    )


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行应用
    run_app(debug=True)
