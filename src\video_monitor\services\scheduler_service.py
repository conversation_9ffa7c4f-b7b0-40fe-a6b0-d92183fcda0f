"""
定时任务调度服务

使用APScheduler实现定时数据同步功能
"""

import logging
from typing import Optional, Callable, Dict, Any
from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

logger = logging.getLogger(__name__)


class SchedulerService:
    """定时任务调度服务类"""
    
    def __init__(self):
        """初始化调度服务"""
        self.scheduler = BackgroundScheduler()
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        self._is_running = False
        
        logger.info("定时任务调度服务初始化完成")
    
    def start(self) -> bool:
        """
        启动调度服务
        
        Returns:
            启动是否成功
        """
        if self._is_running:
            logger.warning("调度服务已在运行")
            return True
        
        try:
            self.scheduler.start()
            self._is_running = True
            logger.info("✅ 定时任务调度服务启动成功")
            return True
        except Exception as e:
            logger.error(f"❌ 启动调度服务失败: {e}")
            return False
    
    def stop(self) -> None:
        """停止调度服务"""
        if not self._is_running:
            return
        
        try:
            self.scheduler.shutdown(wait=True)
            self._is_running = False
            logger.info("✅ 定时任务调度服务已停止")
        except Exception as e:
            logger.error(f"❌ 停止调度服务失败: {e}")
    
    def add_hourly_sync_job(self, sync_callback: Callable[[], Dict[str, Any]], 
                           job_id: str = "hourly_sync") -> bool:
        """
        添加每小时同步任务
        
        Args:
            sync_callback: 同步回调函数
            job_id: 任务ID
            
        Returns:
            添加是否成功
        """
        try:
            # 每小时的第0分钟执行
            trigger = CronTrigger(minute=0)
            
            self.scheduler.add_job(
                func=self._sync_job_wrapper,
                trigger=trigger,
                args=[sync_callback],
                id=job_id,
                name="每小时数据同步",
                replace_existing=True,
                max_instances=1  # 防止重复执行
            )
            
            logger.info(f"✅ 已添加每小时同步任务: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加每小时同步任务失败: {e}")
            return False
    
    def add_interval_sync_job(self, sync_callback: Callable[[], Dict[str, Any]], 
                             interval_minutes: int = 60, 
                             job_id: str = "interval_sync") -> bool:
        """
        添加间隔同步任务
        
        Args:
            sync_callback: 同步回调函数
            interval_minutes: 间隔时间（分钟）
            job_id: 任务ID
            
        Returns:
            添加是否成功
        """
        try:
            trigger = IntervalTrigger(minutes=interval_minutes)
            
            self.scheduler.add_job(
                func=self._sync_job_wrapper,
                trigger=trigger,
                args=[sync_callback],
                id=job_id,
                name=f"每{interval_minutes}分钟数据同步",
                replace_existing=True,
                max_instances=1
            )
            
            logger.info(f"✅ 已添加间隔同步任务: {job_id} (间隔: {interval_minutes}分钟)")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加间隔同步任务失败: {e}")
            return False
    
    def remove_job(self, job_id: str) -> bool:
        """
        移除指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            移除是否成功
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"✅ 已移除任务: {job_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 移除任务失败: {e}")
            return False
    
    def pause_job(self, job_id: str) -> bool:
        """
        暂停指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            暂停是否成功
        """
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"✅ 已暂停任务: {job_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 暂停任务失败: {e}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """
        恢复指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            恢复是否成功
        """
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"✅ 已恢复任务: {job_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 恢复任务失败: {e}")
            return False
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        """
        获取所有任务信息
        
        Returns:
            任务信息列表
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger),
                    "pending": job.pending
                })
            return jobs
        except Exception as e:
            logger.error(f"❌ 获取任务信息失败: {e}")
            return []
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定任务状态
        
        Args:
            job_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger),
                    "pending": job.pending
                }
            return None
        except Exception as e:
            logger.error(f"❌ 获取任务状态失败: {e}")
            return None
    
    def trigger_job_now(self, job_id: str) -> bool:
        """
        立即触发指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            触发是否成功
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.modify(next_run_time=datetime.now())
                logger.info(f"✅ 已触发任务: {job_id}")
                return True
            else:
                logger.warning(f"⚠️ 任务不存在: {job_id}")
                return False
        except Exception as e:
            logger.error(f"❌ 触发任务失败: {e}")
            return False
    
    def _sync_job_wrapper(self, sync_callback: Callable[[], Dict[str, Any]]) -> None:
        """
        同步任务包装器
        
        Args:
            sync_callback: 同步回调函数
        """
        try:
            logger.info("🔄 开始执行定时同步任务...")
            result = sync_callback()
            
            if result.get("success"):
                logger.info(f"✅ 定时同步任务完成: {result}")
            else:
                logger.error(f"❌ 定时同步任务失败: {result}")
                
        except Exception as e:
            logger.error(f"❌ 定时同步任务执行异常: {e}")
    
    def _job_listener(self, event) -> None:
        """
        任务事件监听器
        
        Args:
            event: 任务事件
        """
        if event.exception:
            logger.error(f"❌ 任务执行失败: {event.job_id}, 异常: {event.exception}")
        else:
            logger.debug(f"✅ 任务执行成功: {event.job_id}")
    
    @property
    def is_running(self) -> bool:
        """检查调度服务是否运行中"""
        return self._is_running
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取调度服务状态
        
        Returns:
            状态信息
        """
        return {
            "is_running": self._is_running,
            "jobs_count": len(self.scheduler.get_jobs()) if self._is_running else 0,
            "jobs": self.get_jobs() if self._is_running else []
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
